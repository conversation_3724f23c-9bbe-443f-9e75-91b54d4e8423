import React, { useRef, useCallback, useState, useEffect } from 'react';
import CodeMirror from '@uiw/react-codemirror';
import { EditorView, lineNumbers, Decoration, keymap } from '@codemirror/view';
import { StateField, StateEffect } from '@codemirror/state';

// 样式常量
const FONT_FAMILY = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
const FONT_SIZE = '12px';
const SEARCH_MARGIN = 15;

// 按钮基础样式
const BUTTON_BASE_STYLE = {
  padding: '4px 6px',
  border: '1px solid #d9d9d9',
  borderRadius: '4px',
  background: 'white',
  cursor: 'pointer',
  fontSize: FONT_SIZE
};

const getScrollableParent = (element) => {
  if (!element || typeof window === 'undefined') return null;

  const overflowRegex = /(auto|scroll)/;
  let el = element;

  while (el && el.parentNode && el.nodeType === Node.ELEMENT_NODE) {
    const style = window.getComputedStyle(el);
    if (overflowRegex.test(style.overflow + style.overflowY + style.overflowX)) {
      if (el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth) {
        return el;
      }
    }
    if (el === document.body || el === document.documentElement) break;
    el = el.parentNode;
  }

  // 检查 body 和 documentElement
  for (const elem of [document.body, document.documentElement]) {
    if (el === elem || element === elem) {
      const elemStyle = window.getComputedStyle(elem);
      if (overflowRegex.test(elemStyle.overflow + elemStyle.overflowY + elemStyle.overflowX)) {
        if (elem.scrollHeight > elem.clientHeight || elem.scrollWidth > elem.clientWidth) {
          return elem;
        }
      }
    }
  }

  return null;
};

const SearchPanel = ({ isVisible, onClose, editorRef, containerRef }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [useRegex, setUseRegex] = useState(false);
  const [currentMatch, setCurrentMatch] = useState(0);
  const [totalMatches, setTotalMatches] = useState(0);
  const [fixedPosition, setFixedPosition] = useState({ top: '20px', right: '20px' });
  const searchInputRef = useRef(null);
  const actualPanelRef = useRef(null);

  // 简化的工具栏检测逻辑
  const calculateObstructedHeight = useCallback(() => {
    try {
      let maxObstructedHeight = 0;
      let totalPaddingBottom = 0;

      // 只检查可能的工具栏元素，避免遍历所有DOM元素
      const potentialToolbars = document.querySelectorAll('[class*="toolbar"], [class*="header"], [class*="nav"], header, nav');

      for (const element of potentialToolbars) {
        const style = window.getComputedStyle(element);
        if (style.position === 'fixed' || style.position === 'sticky') {
          const rect = element.getBoundingClientRect();

          // 只考虑顶部区域的工具栏元素
          if (rect.top <= 100 && rect.height > 10 && rect.height < 200 && rect.width > 100) {
            const paddingBottom = parseInt(style.paddingBottom) || 0;
            if (rect.bottom > maxObstructedHeight) {
              maxObstructedHeight = rect.bottom;
              totalPaddingBottom = paddingBottom;
            }
          }
        }
      }

      return {
        obstructedHeight: Math.max(0, maxObstructedHeight),
        toolbarPaddingBottom: totalPaddingBottom
      };
    } catch (error) {
      return { obstructedHeight: 0, toolbarPaddingBottom: 0 };
    }
  }, []);

  // 计算搜索面板的固定位置
  const updatePanelPosition = useCallback(() => {
    if (!isVisible || !containerRef?.current || !editorRef?.current?.view) return;

    const view = editorRef.current.view;
    const editorRect = view.dom.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 获取工具栏遮挡信息
    const { obstructedHeight, toolbarPaddingBottom } = calculateObstructedHeight();

    // 计算编辑器可视区域
    const visibleTop = Math.max(editorRect.top, obstructedHeight, 0);
    const visibleBottom = Math.min(editorRect.bottom, viewportHeight);
    const visibleRight = Math.min(editorRect.right, viewportWidth);
    const visibleLeft = Math.max(editorRect.left, 0);

    // 如果编辑器不可见，隐藏搜索框
    if (visibleTop >= visibleBottom || visibleRight <= visibleLeft) {
      setFixedPosition({ top: '-1000px', right: '-1000px' });
      return;
    }

    // 计算搜索框位置
    const panelWidth = 280;
    const panelHeight = 80;
    let finalTop = Math.max(editorRect.top + SEARCH_MARGIN, obstructedHeight + SEARCH_MARGIN - toolbarPaddingBottom);
    let finalRight = viewportWidth - editorRect.right + SEARCH_MARGIN;

    // 边界检查
    if (finalRight + panelWidth > viewportWidth - visibleLeft) {
      finalRight = Math.max(10, viewportWidth - visibleLeft - panelWidth - 10);
    }
    if (finalTop + panelHeight > visibleBottom) {
      finalTop = Math.max(visibleTop + 10, visibleBottom - panelHeight - 10);
    }

    setFixedPosition({
      top: `${finalTop}px`,
      right: `${finalRight}px`
    });
  }, [isVisible, editorRef, containerRef, calculateObstructedHeight]);

  // 当面板显示时聚焦搜索框并更新位置
  useEffect(() => {
    if (isVisible) {
      if (searchInputRef.current) {
        searchInputRef.current.focus({ preventScroll: true });
      }
      updatePanelPosition();
    }
  }, [isVisible, updatePanelPosition]);

  // 监听滚动和窗口大小变化事件
  useEffect(() => {
    if (!isVisible) return;

    const handleUpdate = () => updatePanelPosition();

    // 获取滚动目标
    const pageScrollTarget = containerRef.current
      ? getScrollableParent(containerRef.current) || document.body
      : window;
    const editorScrollTarget = editorRef?.current?.view?.scrollDOM;

    // 添加事件监听器
    const targets = [
      { target: pageScrollTarget, event: 'scroll' },
      { target: editorScrollTarget, event: 'scroll' },
      { target: window, event: 'resize' }
    ];

    targets.forEach(({ target, event }) => {
      if (target) target.addEventListener(event, handleUpdate, { passive: true });
    });

    return () => {
      targets.forEach(({ target, event }) => {
        if (target) target.removeEventListener(event, handleUpdate);
      });
    };
  }, [isVisible, editorRef, containerRef, updatePanelPosition]);

  // 清除搜索高亮的辅助函数
  const clearHighlights = useCallback(() => {
    console.log('🧹 clearHighlights called');
    const view = editorRef.current?.view;
    if (view) {
      console.log('✅ Dispatching clearSearchHighlights');
      view.dispatch({ effects: clearSearchHighlights.of(null) });
    } else {
      console.log('❌ No view available for clearing highlights');
    }
    setCurrentMatch(0);
    setTotalMatches(0);
    console.log('✅ Reset currentMatch and totalMatches to 0');
  }, [editorRef]);

  // 执行搜索
  const performSearch = useCallback((term, direction = 'next') => {
    console.log('🔍 performSearch called:', { term, direction, editorRef: !!editorRef.current });

    if (!editorRef.current || !term) {
      console.log('❌ Early return: no editor or term');
      clearHighlights();
      return;
    }

    const view = editorRef.current.view;
    if (!view) {
      console.log('❌ No view available');
      return;
    }

    try {
      // 手动实现搜索逻辑
      const doc = view.state.doc;
      const text = doc.toString();
      console.log('📄 Document text length:', text.length);

      let regex;
      if (useRegex) {
        try {
          regex = new RegExp(term, caseSensitive ? 'g' : 'gi');
        } catch (e) {
          console.log('❌ Invalid regex:', e);
          clearHighlights();
          return;
        }
      } else {
        const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        regex = new RegExp(escapedTerm, caseSensitive ? 'g' : 'gi');
      }

      const matches = [...text.matchAll(regex)];
      console.log('🎯 Found matches:', matches.length, matches.map(m => ({ index: m.index, text: m[0] })));
      setTotalMatches(matches.length);

      if (matches.length > 0) {
        // 找到当前光标位置
        const currentPos = view.state.selection.main.head;
        console.log('📍 Current cursor position:', currentPos);

        // 找到最接近的匹配项
        let targetIndex = 0;
        if (direction === 'next') {
          console.log('➡️ Searching NEXT');
          // 查找当前选择区域之后的第一个匹配项
          const currentSelectionEnd = view.state.selection.main.to;
          console.log('📍 Current selection end:', currentSelectionEnd);

          targetIndex = matches.findIndex(match => match.index >= currentSelectionEnd);
          console.log('🔍 First match >= selectionEnd:', targetIndex);
          if (targetIndex === -1) targetIndex = 0; // 循环到开头
          console.log('✅ NEXT target index:', targetIndex);
        } else if (direction === 'prev') {
          console.log('⬅️ Searching PREV');
          // 查找当前选择区域之前的最后一个匹配项
          const currentSelectionStart = view.state.selection.main.from;
          console.log('📍 Current selection start:', currentSelectionStart);

          let foundIndex = -1;
          for (let i = matches.length - 1; i >= 0; i--) {
            console.log(`🔍 Checking match ${i}: index ${matches[i].index} < selectionStart ${currentSelectionStart}?`, matches[i].index < currentSelectionStart);
            if (matches[i].index < currentSelectionStart) {
              foundIndex = i;
              console.log('✅ Found prev match at index:', i);
              break;
            }
          }
          targetIndex = foundIndex === -1 ? matches.length - 1 : foundIndex;
          console.log('✅ PREV target index:', targetIndex, 'foundIndex:', foundIndex);
        }

        console.log('🎯 Final target match:', {
          targetIndex,
          matchIndex: matches[targetIndex].index,
          matchText: matches[targetIndex][0],
          currentMatch: targetIndex + 1,
          totalMatches: matches.length
        });

        setCurrentMatch(targetIndex + 1);

        // 跳转到匹配位置
        const match = matches[targetIndex];
        const from = match.index;
        const to = from + match[0].length;

        console.log('📍 Jumping to position:', { from, to, text: match[0] });

        // 先设置选择和高亮
        view.dispatch({
          selection: { anchor: from, head: to },
          effects: [
            setSearchHighlights.of({ matches, currentIndex: targetIndex })
          ]
        });

        console.log('✅ Selection and highlights set');

        // 强制滚动到匹配位置
        requestAnimationFrame(() => {
          console.log('🔄 Scrolling to match position');
          // 方法1: 使用 CodeMirror 的 scrollIntoView
          view.dispatch({
            effects: EditorView.scrollIntoView(from, { y: "center", yMargin: 100 })
          });

          // 方法2: 备用滚动方法
          setTimeout(() => {
            try {
              const line = view.state.doc.lineAt(from);
              const lineElement = view.domAtPos(line.from);
              if (lineElement && lineElement.node) {
                const element = lineElement.node.nodeType === Node.TEXT_NODE
                  ? lineElement.node.parentElement
                  : lineElement.node;

                if (element && element.scrollIntoView) {
                  element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                  });
                }
              }
            } catch (e) {
              console.log('⚠️ Alternative scroll method failed:', e);
            }
          }, 50);
        });
      } else {
        console.log('❌ No matches found');
        clearHighlights();
      }
    } catch (error) {
      console.error('❌ Search error:', error);
      clearHighlights();
    }
  }, [caseSensitive, useRegex, editorRef, clearHighlights]);

  // 搜索输入变化
  const handleSearchChange = (e) => {
    const term = e.target.value;
    console.log('🔤 Search term changed:', term);
    setSearchTerm(term);
    if (term) {
      console.log('🔍 Performing initial search for:', term);
      performSearch(term);
    } else {
      console.log('🧹 Clearing highlights (empty term)');
      clearHighlights();
    }
  };

  // 当搜索选项变化时重新搜索
  useEffect(() => {
    if (searchTerm) {
      performSearch(searchTerm);
    }
  }, [caseSensitive, useRegex, performSearch]);

  // 键盘事件处理
  const handleKeyDown = (e) => {
    console.log('⌨️ Key pressed:', { key: e.key, shiftKey: e.shiftKey, searchTerm });
    if (e.key === 'Enter') {
      e.preventDefault();
      if (e.shiftKey) {
        console.log('⌨️ Shift+Enter -> PREV');
        performSearch(searchTerm, 'prev');
      } else {
        console.log('⌨️ Enter -> NEXT');
        performSearch(searchTerm, 'next');
      }
    } else if (e.key === 'Escape') {
      console.log('⌨️ Escape -> Close');
      onClose();
    }
  };

  if (!isVisible) return null;

  return (
    <div ref={actualPanelRef} style={{
      position: 'fixed',
      top: fixedPosition.top,
      right: fixedPosition.right,
      background: 'white',
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
      padding: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      zIndex: 1001,
      minWidth: '280px',
      fontSize: FONT_SIZE,
      maxHeight: '200px',
      overflowY: 'auto',
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px', marginBottom: '4px' }}>
        <input
          ref={searchInputRef}
          type="text"
          value={searchTerm}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          placeholder="搜索..."
          style={{
            flex: 1,
            padding: '4px 6px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            fontSize: FONT_SIZE
          }}
        />
        <button
          onClick={() => {
            console.log('🔼 PREV button clicked!', { searchTerm, totalMatches, disabled: !searchTerm || totalMatches === 0 });
            performSearch(searchTerm, 'prev');
          }}
          disabled={!searchTerm || totalMatches === 0}
          style={BUTTON_BASE_STYLE}
          title="上一个 (Shift+Enter)"
        >
          ↑
        </button>
        <button
          onClick={() => {
            console.log('🔽 NEXT button clicked!', { searchTerm, totalMatches, disabled: !searchTerm || totalMatches === 0 });
            performSearch(searchTerm, 'next');
          }}
          disabled={!searchTerm || totalMatches === 0}
          style={BUTTON_BASE_STYLE}
          title="下一个 (Enter)"
        >
          ↓
        </button>
        <button
          onClick={onClose}
          style={BUTTON_BASE_STYLE}
          title="关闭 (Esc)"
        >
          ✕
        </button>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '11px', color: '#666' }}>
        <label style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
          <input
            type="checkbox"
            checked={caseSensitive}
            onChange={(e) => setCaseSensitive(e.target.checked)}
            style={{ margin: 0 }}
          />
          区分大小写
        </label>
        <label style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
          <input
            type="checkbox"
            checked={useRegex}
            onChange={(e) => setUseRegex(e.target.checked)}
            style={{ margin: 0 }}
          />
          正则表达式
        </label>
        <span style={{ marginLeft: 'auto' }}>
          {totalMatches > 0 ? `${currentMatch}/${totalMatches}` : '无匹配'}
        </span>
      </div>
    </div>
  );
};

// 创建占位符高亮装饰器
const placeholderMark = Decoration.mark({
  class: 'cm-placeholder-mark',
  attributes: {
    style: 'color: #999; font-style: italic;'
  }
});

// 创建搜索高亮装饰器
const searchHighlightMark = Decoration.mark({
  class: 'cm-search-highlight',
  attributes: {
    style: 'background-color: yellow; color: black;'
  }
});

const currentSearchHighlightMark = Decoration.mark({
  class: 'cm-current-search-highlight',
  attributes: {
    style: 'background-color: orange; color: black; border: 1px solid #ff6600;'
  }
});

// 搜索高亮状态效果
const setSearchHighlights = StateEffect.define();
const clearSearchHighlights = StateEffect.define();

// 创建状态字段来管理搜索高亮装饰
const searchHighlightField = StateField.define({
  create() {
    return Decoration.none;
  },
  update(decorations, tr) {
    decorations = decorations.map(tr.changes);

    for (let effect of tr.effects) {
      if (effect.is(setSearchHighlights)) {
        const { matches, currentIndex } = effect.value;
        const newDecorations = [];

        matches.forEach((match, index) => {
          const from = match.index;
          const to = from + match[0].length;
          const mark = index === currentIndex ? currentSearchHighlightMark : searchHighlightMark;
          newDecorations.push(mark.range(from, to));
        });

        decorations = Decoration.set(newDecorations);
      } else if (effect.is(clearSearchHighlights)) {
        decorations = Decoration.none;
      }
    }

    return decorations;
  },
  provide: f => EditorView.decorations.from(f)
});

// 创建状态字段来管理占位符装饰
const placeholderField = StateField.define({
  create() {
    return Decoration.none;
  },
  update(decorations, tr) {
    decorations = decorations.map(tr.changes);
    const doc = tr.state.doc;
    const newDecorations = [];
    const regex = /\{null\}/g;

    for (let i = 1; i <= doc.lines; i++) {
      const line = doc.line(i);
      let match;
      while ((match = regex.exec(line.text)) !== null) {
        const from = line.from + match.index;
        const to = from + match[0].length;
        newDecorations.push(placeholderMark.range(from, to));
      }
      regex.lastIndex = 0; // 重置正则表达式状态
    }

    return Decoration.set(newDecorations);
  },
  provide: f => EditorView.decorations.from(f)
});

/**
 * 增强的TextArea组件，使用CodeMirror实现，支持行号和防抖。
 * 注意：原有的自定义快捷键（剪切/复制行、粘贴到新行）和手动历史记录已被移除，
 * CodeMirror提供内置的撤销/重做功能。
 *
 * autoSize 属性说明：
 * - 如果传入 true，则启用自适应高度，无最小行数限制
 * - 如果传入 { minRows: number }，则启用自适应高度，并设置最小行数
 * - 如果不传入或传入 false，则使用固定高度
 */
const EnhanceTextArea = React.forwardRef((props, ref) => {
  const {
    onChange,
    value: propValue,
    defaultValue,
    debounceDelay = 300,
    showLineNumbers = true,
    style,
    autoSize,
    ...restProps
  } = props;
  const [localValue, setLocalValue] = useState(propValue ?? defaultValue ?? '');
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const debounceTimerRef = useRef(null);
  const isUserInputRef = useRef(false);
  const editorRef = useRef(null);
  const containerRef = useRef(null);

  // 解析 autoSize 配置
  const autoSizeConfig = React.useMemo(() => {
    if (!autoSize) return null;
    if (autoSize === true) return { enabled: true, minRows: 1 };
    if (typeof autoSize === 'object' && autoSize.minRows) {
      return { enabled: true, minRows: autoSize.minRows };
    }
    return null;
  }, [autoSize]);

  // Debounced onChange handler
  const debouncedOnChange = useCallback((val) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    debounceTimerRef.current = setTimeout(() => {
      if (onChange) {
        const mockEvent = {
          target: {
            value: val
          }
        };
        onChange(mockEvent);
        isUserInputRef.current = false;
      }
    }, debounceDelay);
  }, [onChange, debounceDelay]);

  // 当外部值变化时更新本地状态
  useEffect(() => {
    if (propValue !== undefined && !isUserInputRef.current && propValue !== localValue) {
      setLocalValue(propValue);
    }
  }, [propValue]);

  // Handle CodeMirror changes
  const handleCodeMirrorChange = useCallback((val) => {
    isUserInputRef.current = true;
    setLocalValue(val);
    debouncedOnChange(val);
  }, [debouncedOnChange]);

  // 关闭搜索面板
  const handleCloseSearch = useCallback(() => {
    setIsSearchVisible(false);
  }, []);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // CodeMirror 配置
  const extensions = React.useMemo(() => {
    const baseExtensions = [
      EditorView.lineWrapping,
      placeholderField,
      searchHighlightField,
      keymap.of([
        {
          key: 'Mod-f',
          preventDefault: true,
          run: () => {
            setIsSearchVisible(true);
            return true;
          }
        }
      ]),
      EditorView.theme({
        '&, .cm-editor, .cm-scroller, .cm-content, .cm-line': {
          fontFamily: FONT_FAMILY,
          fontSize: FONT_SIZE
        },
        '.cm-content': {
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
          whiteSpace: 'pre-wrap',
          ...(autoSizeConfig && {
            minHeight: `${autoSizeConfig.minRows * 1.6}em`,
            padding: '8px 12px'
          })
        },
        '.cm-line': {
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        },
        '.cm-editor.cm-focused': {
          outline: 'none'
        }
      })
    ];

    // 自适应高度主题
    if (autoSizeConfig) {
      baseExtensions.push(
        EditorView.theme({
          '&': {
            height: 'auto'
          },
          '.cm-editor': {
            height: 'auto'
          },
          '.cm-scroller': {
            overflow: 'visible',
            height: 'auto'
          }
        })
      );
    }

    return baseExtensions;
  }, [autoSizeConfig, setIsSearchVisible]);

  if (showLineNumbers) {
    extensions.push(lineNumbers());
  }

  // 编辑器样式
  const editorStyle = React.useMemo(() => ({
    border: '1px solid #d9d9d9',
    borderRadius: '6px',
    overflow: autoSizeConfig ? 'visible' : 'auto',
    width: '100%',
    maxWidth: '100%',
    boxSizing: 'border-box',
    fontSize: FONT_SIZE,
    ...(autoSizeConfig ? {} : { height: '200px' }),
    ...style
  }), [autoSizeConfig, style]);

  return (
    <div ref={containerRef} style={{ position: 'relative', width: '100%' }}>
      <CodeMirror
        ref={(editor) => {
          editorRef.current = editor;
          if (ref) {
            if (typeof ref === 'function') {
              ref(editor);
            } else {
              ref.current = editor;
            }
          }
        }}
        value={localValue}
        onChange={handleCodeMirrorChange}
        extensions={extensions}
        style={editorStyle}
        height={autoSizeConfig ? undefined : '200px'}
        basicSetup={{
          lineNumbers: showLineNumbers,
          foldGutter: false,
          dropCursor: false,
          allowMultipleSelections: false,
          indentOnInput: true,
          bracketMatching: true,
          closeBrackets: true,
          autocompletion: true,
          highlightSelectionMatches: false,
          searchKeymap: false
        }}
        {...restProps}
        readOnly={props.readOnly}
        placeholder={props.placeholder}
      />
      <SearchPanel
        isVisible={isSearchVisible}
        onClose={handleCloseSearch}
        editorRef={editorRef}
        containerRef={containerRef}
      />
    </div>
  );
});

EnhanceTextArea.displayName = 'EnhanceTextArea';

export default EnhanceTextArea;
