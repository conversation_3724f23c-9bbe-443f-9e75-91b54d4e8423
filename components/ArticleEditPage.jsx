import React, { useState, useRef, useMemo, useCallback, memo, useEffect } from 'react';
import {
  Button, Space, Divider, Row, Col, InputNumber,
  Popconfirm, App, Input, Typography, Layout
} from 'antd';
import {
  EditOutlined, CloseOutlined, <PERSON>boltOutlined, SaveOutlined, ClearOutlined, RobotOutlined, FileTextOutlined, PauseOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import _, { isEqual, isEmpty, cloneDeep } from 'lodash'; // Import cloneDeep
// import OpenAI from 'openai'; // OpenAI import might be redundant
import { useStore } from '../stores/index.js';
import { shallow } from 'zustand/shallow';
import { sliceContentByIds, html2content, content2Html, checkTagCountMismatch, getFirstTranslatedItemText, getAllTextFromChunks, corsFailedApiUrls, getOpenAIClientForCalling, formatTextWithAI, streamTextWithAI } from '../utils/index.js'; // Import new utils, added streamTextWithAI
import { apiBaseUrl, defaultTranslationPromptText, defaultTitleGenerationPromptText, defaultSummaryGenerationPromptText, defaultReferenceParsePromptText } from '../utils/constant.js';

import './ArticleEditPage.css';
import TranslatedChunkDisplay from './TranslatedChunkDisplay';
import ChunkRenderer from './ChunkRenderer';
import ThinkDisplay from './ThinkDisplay';
import TerminologySyncModal from './TerminologySyncModal'; // 导入术语同步模态框
import EnhanceTextArea from './EnhanceTextArea'; // 导入增强的TextArea组件
// import ArticleControls from './ArticleControls'; // Merged into ArticleEditPage
// import ArticleTitle from './ArticleTitle'; // Merged into ArticleEditPage
import { useArticleDataFetching } from '../hooks/useArticleDataFetching.js';
import { useChunkManagement } from '../hooks/useChunkManagement.js';
import { useTranslationControls } from '../hooks/useTranslationControls.js';
import { useEditControls } from '../hooks/useEditControls.js';
import { useSmartScroll } from '../hooks/useSmartScroll.js'; // 导入新的滚动定位Hook
import $ from 'jquery';

// 辅助函数 splitSingleParagraph, splitTextIntoChunks, processInParallelWithMessage 已移至 Sidebar.jsx

const ArticleEditPage = ({ showTranslationOnly = false }) => {
  const paramsForLog = useParams(); // Get params here for logging mount/unmount with uuid

  useEffect(() => {
    return () => {
    };
  }, [paramsForLog.uuid]); // Re-run if uuid changes, effectively logging "remount" or "key change"
  // 添加渲染计数器
  const renderCountRef = useRef(0);
  renderCountRef.current += 1;
  const renderCount = renderCountRef.current;


  const navigate = useNavigate();
  const { message: messageApi, modal } = App.useApp();

  const articleFromStore = useStore(state => state.article);
  // const translated = useStore(state => state.translated || {}); // Not directly used, allChunkTranslatedItems from useChunkManagement is used
  const hasTranslations = useStore(state => Object.keys(state.translated || {}).length > 0);
  const storeApis = useStore(state => state.apis);
  const storeDefaultApiModel = useStore(state => state.defaultApiModel);
  // const storeTranslationPrompt = useStore(state => state.translationPrompt); // Used by translateChunk in store
  const storeParseTextPrompt = useStore(state => state.parseTextPrompt);
  const storeTitleGenerationPrompt = useStore(state => state.titleGenerationPrompt);
  const storeSummaryGenerationPrompt = useStore(state => state.summaryGenerationPrompt);
  const storeReferenceParsePrompt = useStore(state => state.referenceParsePrompt);
  const appendGlossaryToPrompt = useStore(state => state.appendGlossaryToPrompt);
  const user = useStore(state => state.user, shallow); // This user will be used for userChunkSize
  const initialContentProp = useStore(state => state.content); // Get content from store
  const userChunkSize = user?.chunkSize ?? 5; // Derive userChunkSize from store's user
  const currentTranslationControllers = useStore(state => state.currentTranslationControllers);
  const isTranslatingAllActive = useStore(state => state.isTranslatingAllActive);
  const isTranslationGloballyCancelled = useStore(state => state.isTranslationGloballyCancelled);
  // const indicesToTranslate = useStore(state => state.indicesToTranslate); // Managed by translateAll in store
  // const contentFromStore = useStore(state => state.content); // Not directly used, chunked from useChunkManagement is used

  // const logout = useStore(state => state.logout); // Not used in this component
  const setDefaultApiModel = useStore(state => state.setDefaultApiModel);
  // const translateChunk = useStore(state => state.translateChunk); // Used by useTranslationControls
  // const clearAllTranslations = useStore(state => state.clearAllTranslations); // Used by useTranslationControls
  const setUser = useStore(state => state.setUser);
  // const saveChunkSize = useStore(state => state.saveChunkSize); // Used by useChunkManagement
  // const translateAll = useStore(state => state.translateAll); // Used by useTranslationControls
  // const cancelAllTranslations = useStore(state => state.cancelAllTranslations); // Used by useTranslationControls
  // const cancelSingleTranslation = useStore(state => state.cancelSingleTranslation); // Used by useTranslationControls
  const updateOriginalChunk = useStore(state => state.updateOriginalChunk); // Passed to useEditControls
  const updateTranslatedChunk = useStore(state => state.updateTranslatedChunk); // Passed to useEditControls
  const setTranslated = useStore(state => state.setTranslated); // Roo: Import the new action
  const deleteChunkAction = useStore(state => state.deleteChunk);
  // const clearTranslatedChunkAction = useStore(state => state.clearTranslatedChunk); // Used by useTranslationControls
  // const removeTranslationController = useStore(state => state.removeTranslationController); // Internal to store
  // const clearStreamingChunkText = useStore(state => state.clearStreamingChunkText); // Internal to store
  // const setIndicesToTranslate = useStore(state => state.setIndicesToTranslate); // Internal to store
  const saveArticle = useStore(state => state.saveArticle);
  const setContent = useStore(state => state.setContent); // Used by useChunkManagement and full text edit
  // const setArticle = useStore(state => state.setArticle); // Used by useArticleDataFetching
  const isTerminologyModalOpen = useStore(state => state.isTerminologyModalOpen);
  const terminologyListForModal = useStore(state => state.terminologyListForModal);
  const terminologyExtractionProgress = useStore(state => state.terminologyExtractionProgress);
  const startTerminologySyncProcess = useStore(state => state.startTerminologySyncProcess);
  const applyTerminologySync = useStore(state => state.applyTerminologySync);
  const closeTerminologyModal = useStore(state => state.closeTerminologyModal);

  // 合并编辑相关状态
  const [isMergedOriginalEditing, setIsMergedOriginalEditing] = useState(false);
  const [isMergedTranslatedEditing, setIsMergedTranslatedEditing] = useState(false);
  const [mergedOriginalText, setMergedOriginalText] = useState('');
  const [mergedTranslatedText, setMergedTranslatedText] = useState('');

  const params = useParams();
  const { uuid } = params;
  const [concurrencyLevel, setConcurrencyLevel] = useState(user?.concurrencyLevel ?? 3);
  const [articleTitle, setArticleTitle] = useState(null); // 使用 null 表示未加载状态
  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false);
  const [articleSummary, setArticleSummary] = useState(null); // AI总结内容
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false); // AI总结生成状态
  const [isSummaryEditing, setIsSummaryEditing] = useState(false); // 总结编辑状态
  const [editingSummaryText, setEditingSummaryText] = useState(''); // 编辑中的总结文本
  const [streamingSummaryText, setStreamingSummaryText] = useState(''); // 流式输出的总结文本
  const [summaryThinkContent, setSummaryThinkContent] = useState(''); // AI总结的think过程内容
  const [isSummaryThinking, setIsSummaryThinking] = useState(false); // AI总结是否在思考中
  const [summaryThinkCompleted, setSummaryThinkCompleted] = useState(false); // AI总结think过程是否完成
  const [summaryAbortController, setSummaryAbortController] = useState(null); // AI总结的中断控制器
  const [isStoppingSummary, setIsStoppingSummary] = useState(false); // 停止总结的状态
  const [isRefsEditing, setIsRefsEditing] = useState(false); // 参考编辑状态
  const [editingRefsText, setEditingRefsText] = useState(''); // 编辑中的参考文本
  const [isParsingRefs, setIsParsingRefs] = useState(false); // AI解析参考状态
  const [isCitationEditing, setIsCitationEditing] = useState(false); // 来源编辑状态
  const [editingCitationText, setEditingCitationText] = useState(''); // 编辑中的来源文本
  const [refsData, setRefsData] = useState(''); // 存储的参考数据
  const [citationData, setCitationData] = useState(''); // 存储的来源数据
  const debounceTimerRef = useRef(null); // Still needed for cleaning up debouncedSetEditTextHook
  const fullPageEditorRef = useRef(null); // 新增：用于全文编辑器的 ref

  // 工具栏高度相关状态
  const toolbarRef = useRef(null);
  const pageContainerRef = useRef(null);
  // Roo: Add state for sticky status and ref for the sticky header
  const [isHeaderSticky, setIsHeaderSticky] = useState(false);
  const [isStickyDisabled, setIsStickyDisabled] = useState(false); // 完全禁用sticky
  const stickyHeaderRef = useRef(null);

  // 使用智能滚动Hook
  const { executeSmartScroll, createScrollTarget, calculateTargetPosition } = useSmartScroll(
    stickyHeaderRef,
    isHeaderSticky,
    isStickyDisabled
  );

  useEffect(() => {
    const container = pageContainerRef.current;
    const toolbar = toolbarRef.current;

    // Refs might not be ready on first render, so we check.
    if (!container || !toolbar) {
      return;
    }

    const updateToolbarHeight = () => {
      // 使用 getBoundingClientRect().height 获取更精确的高度
      const rect = toolbar.getBoundingClientRect();
      const height = Math.round(rect.height); // 四舍五入到整数像素
      container.style.setProperty('--toolbar-height', `${height}px`);
    };

    // 立即执行一次初始设置，确保CSS变量被设置
    updateToolbarHeight();

    // Use ResizeObserver to watch for toolbar height changes.
    const resizeObserver = new ResizeObserver(() => {
      updateToolbarHeight();
      // 立即再次检查CSS变量，确保快速修复
      setTimeout(updateToolbarHeight, 10);
      setTimeout(updateToolbarHeight, 50);
    });
    resizeObserver.observe(toolbar);

    // 延迟更新以确保快速修复
    const timers = [
      setTimeout(updateToolbarHeight, 50),
      setTimeout(updateToolbarHeight, 100),
      setTimeout(updateToolbarHeight, 200),
      setTimeout(updateToolbarHeight, 500)
    ];

    // Cleanup on unmount.
    return () => {
      resizeObserver.disconnect();
      timers.forEach(clearTimeout);
    };
  }, []); // 只运行一次，但在内部检查refs的可用性



  // Roo: useEffect for IntersectionObserver to track actual sticky state of the header
  useEffect(() => {
    let observer = null;
    let checkTimer = null;

    const setupObserver = () => {
      const headerElement = stickyHeaderRef.current;
      if (!headerElement) {
        // 如果ref还不可用，设置定时器重试
        checkTimer = setTimeout(setupObserver, 100);
        return;
      }

      // 使用一个更大的 rootMargin 来确保观察器能够正确触发，不依赖于精确的工具栏高度
      // 这样可以避免工具栏高度变化时需要重新创建观察器的问题
      observer = new IntersectionObserver(
        ([entry]) => {

          // 在每次IntersectionObserver触发时检查和修复CSS变量
          const container = pageContainerRef.current;
          const toolbar = toolbarRef.current;

          if (container && toolbar) {
            const currentHeight = container.style.getPropertyValue('--toolbar-height');
            const rect = toolbar.getBoundingClientRect();
            const actualHeight = Math.round(rect.height);

            if (!currentHeight || currentHeight !== `${actualHeight}px`) {
              container.style.setProperty('--toolbar-height', `${actualHeight}px`);
            }
          }

          // 在回调中动态计算当前的 sticky 位置
          const currentComputedTopStyle = getComputedStyle(headerElement).top;
          const currentTopOffset = parseFloat(currentComputedTopStyle);

          if (isNaN(currentTopOffset)) {
            return;
          }

          // 检查是否已经滚动到chunks区域之外（比如到了"来源"和"参考"区域）
          const chunksContainer = document.querySelector('.t-content');
          let isScrolledPastChunks = false;

          if (chunksContainer) {
            const chunksRect = chunksContainer.getBoundingClientRect();
            const chunksBottom = chunksRect.bottom;

            // 简化的检测逻辑：当chunks容器的底部完全离开视口时，取消sticky
            // 如果chunks容器的底部在视口顶部以上（即chunksBottom < 0），说明已经滚动过了chunks区域
            const isScrolledPastChunksObserver = chunksBottom < 0;

            if (isScrolledPastChunksObserver) {
              isScrolledPastChunks = true;
            }
          }

          // 如果已经滚动到chunks区域之外，完全禁用sticky
          if (isScrolledPastChunks) {
            setIsHeaderSticky(false);
            setIsStickyDisabled(true);
            return;
          } else {
            // 如果chunks在视口内，重新启用sticky
            setIsStickyDisabled(false);
          }

          // 改进的 sticky 状态判断逻辑
          // 1. 当元素接近 sticky 位置时（在容差范围内），认为是 sticky
          // 2. 增加容差值以处理慢速滚动时的小幅波动
          // 3. 考虑 intersectionRatio 和元素位置来辅助判断
          const tolerance = 20; // 增加容差到20px，处理慢速滚动和不同屏幕尺寸
          const distanceFromStickyPosition = Math.abs(entry.boundingClientRect.top - currentTopOffset);

          // 基础判断：距离 sticky 位置是否在容差范围内
          const isNearStickyPosition = distanceFromStickyPosition <= tolerance;

          // 辅助判断1：当 intersectionRatio 小于 1 时，通常表示元素部分被遮挡，可能处于 sticky 状态
          const isPartiallyVisible = entry.intersectionRatio < 1.0 && entry.intersectionRatio > 0;

          // 辅助判断2：当元素的top值等于或非常接近计算的sticky位置时，认为是sticky
          const isAtStickyPosition = Math.abs(entry.boundingClientRect.top - currentTopOffset) <= 2;

          // 辅助判断3：当元素的top值小于等于sticky位置时，很可能已经sticky了
          const isAboveStickyPosition = entry.boundingClientRect.top <= currentTopOffset + 5;

          // 综合判断：满足任一条件即认为是sticky
          const isActuallySticky = isNearStickyPosition || isAtStickyPosition ||
                                  (isAboveStickyPosition && entry.intersectionRatio >= 0) ||
                                  (distanceFromStickyPosition <= tolerance * 1.5 && isPartiallyVisible);

          setIsHeaderSticky(isActuallySticky);
        },
        {
          root: null,
          // 使用一个更大的 rootMargin，确保在各种工具栏高度下都能正确触发
          // -108px 调整为新的sticky位置（原-120px + 12px向上移动）
          rootMargin: "-108px 0px 0px 0px",
          threshold: [0, 0.01, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95, 0.99, 1.0], // 增加更多阈值以获得更精确的检测
        }
      );

      observer.observe(headerElement);
    };

    // 开始设置observer
    setupObserver();

    return () => {
      if (observer) {
        observer.disconnect();
      }
      if (checkTimer) {
        clearTimeout(checkTimer);
      }
    };
  }, []); // 空依赖数组，只在组件挂载时运行一次，然后通过定时器检查ref是否可用


  // State and handlers for Title Editing (migrated from ArticleTitle.jsx)
  const [isTitleEditing, setIsTitleEditing] = useState(false);
  const [editingTitleText, setEditingTitleText] = useState('');
  const {
    chunked,
    allChunkTranslatedItems,
    displayedChunkSize,
    handleDisplayChunkSizeChange,
    handleChunkSizeChangeImmediate,
  } = useChunkManagement(initialContentProp, userChunkSize);

  const locationPathname = window.location.pathname;
  const { isLoading: dataFetchingLoading } = useArticleDataFetching(uuid, locationPathname);

  // 计算标题，现在可以安全地使用 allChunkTranslatedItems 和 chunked
  const computedTitle = useMemo(() => {

    // Ensure uuid is available from params for comparison
    const currentUuidFromParams = params.uuid;

    if (articleFromStore && articleFromStore.uuid === currentUuidFromParams) {
      const currentArticleTitle = articleFromStore.title;
      if (currentArticleTitle && typeof currentArticleTitle === 'string' && currentArticleTitle.trim() !== "") {
        return currentArticleTitle;
      } else {
        const firstTranslatedText = getFirstTranslatedItemText(allChunkTranslatedItems);
        if (!firstTranslatedText && chunked && chunked.length > 0) {
          const firstOriginalText = getAllTextFromChunks([chunked[0]]).substring(0, 100).trim();
          return firstOriginalText || '';
        } else {
          return firstTranslatedText || '';
        }
      }
    } else if (dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams))) {
      return ''; // 返回空字符串而不是null，避免标题区域高度变化
    } else {
      return ''; // 无数据
    }
  }, [articleFromStore, allChunkTranslatedItems, chunked, dataFetchingLoading]);

  // 计算是否正在加载标题
  const isTitleLoading = useMemo(() => {
    const currentUuidFromParams = params.uuid;
    return dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams));
  }, [articleFromStore, dataFetchingLoading, params.uuid]);

  // 计算总结
  const computedSummary = useMemo(() => {
    const currentUuidFromParams = params.uuid;

    if (articleFromStore && articleFromStore.uuid === currentUuidFromParams) {
      return articleFromStore.summary || null;
    } else if (dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams))) {
      return null; // 加载中或等待store同步
    } else {
      return null; // 无数据
    }
  }, [articleFromStore, dataFetchingLoading]);

  // 计算refs和citation
  const computedRefs = useMemo(() => {
    const currentUuidFromParams = params.uuid;

    if (articleFromStore && articleFromStore.uuid === currentUuidFromParams) {
      return articleFromStore.refs || null;
    } else if (dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams))) {
      return null; // 加载中或等待store同步
    } else {
      return null; // 无数据
    }
  }, [articleFromStore, dataFetchingLoading]);

  const computedCitation = useMemo(() => {
    const currentUuidFromParams = params.uuid;

    if (articleFromStore && articleFromStore.uuid === currentUuidFromParams) {
      return articleFromStore.citation || null;
    } else if (dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams))) {
      return null; // 加载中或等待store同步
    } else {
      return null; // 无数据
    }
  }, [articleFromStore, dataFetchingLoading]);

  // 记录chunked和allChunkTranslatedItems的变化
  const prevChunkedRef = useRef(null);
  const prevAllChunkTranslatedItemsRef = useRef(null);

  useEffect(() => {
    const prevChunked = prevChunkedRef.current;
    const chunkedChanged = prevChunked !== chunked;
    const chunkedLengthChanged = prevChunked?.length !== chunked?.length;


    if (chunkedChanged) {
      prevChunkedRef.current = chunked;
    }
  }, [chunked, renderCount]);

  useEffect(() => {
    const prevItems = prevAllChunkTranslatedItemsRef.current;
    const itemsChanged = prevItems !== allChunkTranslatedItems;
    const itemsLengthChanged = prevItems?.length !== allChunkTranslatedItems?.length;


    if (itemsChanged) {
      prevAllChunkTranslatedItemsRef.current = allChunkTranslatedItems;
    }
  }, [allChunkTranslatedItems, renderCount]);

  // 确保CSS变量在组件完全渲染后被设置
  useEffect(() => {
    const ensureCSSVariableSet = () => {
      const container = pageContainerRef.current;
      const toolbar = toolbarRef.current;

      if (container && toolbar) {
        const currentHeight = container.style.getPropertyValue('--toolbar-height');
        const rect = toolbar.getBoundingClientRect();
        const actualHeight = Math.round(rect.height);

        if (!currentHeight || currentHeight !== `${actualHeight}px`) {
          container.style.setProperty('--toolbar-height', `${actualHeight}px`);
        }
      } else {
        setTimeout(ensureCSSVariableSet, 100);
      }
    };

    ensureCSSVariableSet();

    const timers = [
      setTimeout(ensureCSSVariableSet, 50),
      setTimeout(ensureCSSVariableSet, 100),
      setTimeout(ensureCSSVariableSet, 200),
      setTimeout(ensureCSSVariableSet, 500)
    ];

    return () => timers.forEach(clearTimeout);
  }, [chunked, allChunkTranslatedItems]);

  // 添加滚动事件监听器来确保在慢速滚动时也能修复缝隙和sticky状态
  useEffect(() => {
    let scrollTimer = null;

    const handleScroll = (event) => {
      // 获取实际的滚动位置
      let scrollTop = 0;
      let scrollContainer = null;

      if (event && event.target) {
        // 如果是特定容器的滚动事件
        scrollContainer = event.target;
        scrollTop = event.target.scrollTop || 0;
      } else {
        // 如果是window滚动事件，检查各种可能的滚动容器
        scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0;
        scrollContainer = window;
      }

      // 清除之前的定时器
      if (scrollTimer) {
        clearTimeout(scrollTimer);
      }

      // 立即检查是否滚动到chunks区域之外（不等待定时器）
      const chunksContainer = document.querySelector('.t-content');
      if (chunksContainer) {
        const chunksRect = chunksContainer.getBoundingClientRect();
        const chunksBottom = chunksRect.bottom;

        // 简化的检测逻辑：当chunks容器的底部完全离开视口时，取消sticky
        // 如果chunks容器的底部在视口顶部以上（即chunksBottom < 0），说明已经滚动过了chunks区域
        const isScrolledPastChunks = chunksBottom < 0;

        // 只有当滚动过chunks区域时才强制取消sticky，否则让IntersectionObserver处理
        if (isScrolledPastChunks) {
          setIsHeaderSticky(false);
          setIsStickyDisabled(true);
          return; // 直接返回，不需要进一步处理
        } else {
          // 如果chunks在视口内，重新启用sticky
          setIsStickyDisabled(false);
        }
      }

      // 设置新的定时器，在滚动停止后检查CSS变量和sticky状态
      scrollTimer = setTimeout(() => {
        const container = pageContainerRef.current;
        const toolbar = toolbarRef.current;
        const stickyHeader = stickyHeaderRef.current;

        if (container && toolbar) {
          // 1. 修复CSS变量
          const currentHeight = container.style.getPropertyValue('--toolbar-height');
          const rect = toolbar.getBoundingClientRect();
          const actualHeight = Math.round(rect.height);

          if (!currentHeight || currentHeight !== `${actualHeight}px`) {
            container.style.setProperty('--toolbar-height', `${actualHeight}px`);
          }

          // 2. 检查和修复sticky状态
          if (stickyHeader) {
            // 首先检查是否已经滚动到chunks区域之外
            const chunksContainer = document.querySelector('.t-content');
            let isScrolledPastChunks = false;

            if (chunksContainer) {
              const chunksRect = chunksContainer.getBoundingClientRect();
              const chunksBottom = chunksRect.bottom;

              // 简化的检测逻辑：当chunks容器的底部完全离开视口时，取消sticky
              // 如果chunks容器的底部在视口顶部以上（即chunksBottom < 0），说明已经滚动过了chunks区域
              const isScrolledPastChunksTimeout = chunksBottom < 0;

              if (isScrolledPastChunksTimeout) {
                isScrolledPastChunks = true;
              }
            }

            // 如果已经滚动到chunks区域之外，完全禁用sticky
            if (isScrolledPastChunks) {
              setIsHeaderSticky(false);
              setIsStickyDisabled(true);
            } else {
              // 如果chunks在视口内，重新启用sticky
              setIsStickyDisabled(false);
              // 正常的sticky状态检测逻辑
              const stickyRect = stickyHeader.getBoundingClientRect();

              // 计算是否应该处于sticky状态
              const tolerance = 8;
              const currentComputedTop = parseFloat(getComputedStyle(stickyHeader).top);
              const distanceFromStickyPosition = Math.abs(stickyRect.top - currentComputedTop);
              const shouldBeSticky = distanceFromStickyPosition <= tolerance;

              // 检查当前状态
              const currentlySticky = stickyHeader.classList.contains('is-actually-sticky');

              if (shouldBeSticky !== currentlySticky) {
                setIsHeaderSticky(shouldBeSticky);
              }
            }
          }
        }
      }, 50); // 滚动停止50ms后检查，比之前更快
    };

    // 添加滚动事件监听器
    window.addEventListener('scroll', handleScroll, { passive: true });

    // 也尝试监听document的滚动事件
    document.addEventListener('scroll', handleScroll, { passive: true });

    // 检查是否有其他可能的滚动容器，并为每个容器添加专门的事件处理器
    const possibleScrollContainers = [
      document.body,
      document.documentElement,
      document.querySelector('.ant-layout'),
      document.querySelector('.ant-layout-content'),
      document.querySelector('.article-edit-container'),
      document.querySelector('.site-layout') // MainLayout中的Layout组件
    ];

    const scrollHandlers = [];
    possibleScrollContainers.forEach((container, index) => {
      if (container) {
        // 为每个容器创建专门的处理器
        const containerHandler = (event) => {
          handleScroll(event);
        };

        container.addEventListener('scroll', containerHandler, { passive: true });
        scrollHandlers.push({ container, handler: containerHandler });
      }
    });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('scroll', handleScroll);

      // 清理所有容器的事件监听器
      scrollHandlers.forEach(({ container, handler }) => {
        if (container) {
          container.removeEventListener('scroll', handler);
        }
      });

      if (scrollTimer) {
        clearTimeout(scrollTimer);
      }
    };
  }, []); // 只运行一次

  // Define handleTitleEdit first as handleSaveTitleEdit depends on it.
  const handleTitleEdit = useCallback(async (newTitle) => {
    const trimmedTitle = typeof newTitle === 'string' ? newTitle.trim() : '';
    let titleToSave = trimmedTitle;
    if (!trimmedTitle) {
      const firstTranslatedItemText = getFirstTranslatedItemText(allChunkTranslatedItems);
      titleToSave = firstTranslatedItemText || '';
    }
    setArticleTitle(titleToSave);
    // setIsTitleEditing(false); // Managed by ArticleTitle component

    const currentArticleFromStore = useStore.getState().article;

    if (currentArticleFromStore && currentArticleFromStore.uuid) {
      try {
        await saveArticle({
          title: titleToSave
        });
      } catch (error) {
        messageApi.error(`保存标题失败: ${error.message}`);
      }
    }
  }, [allChunkTranslatedItems, saveArticle, messageApi]);

  useEffect(() => {
    if (isTitleEditing) {
      // When editing starts, populate editing text with the current article title
      setEditingTitleText(articleTitle || "");
    }
  }, [articleTitle, isTitleEditing]); // Rerun if articleTitle changes while editing or editing starts

  useEffect(() => {
    if (isSummaryEditing) {
      // When editing starts, populate editing text with the current article summary
      setEditingSummaryText(articleSummary || "");
    }
  }, [articleSummary, isSummaryEditing]); // Rerun if articleSummary changes while editing or editing starts

  const handleSaveTitleEdit = useCallback(() => {
    if (typeof editingTitleText === 'string') {
      handleTitleEdit(editingTitleText.trim()); // handleTitleEdit is now defined above
    }
    setIsTitleEditing(false);
  }, [editingTitleText, handleTitleEdit]);

  const startTitleEdit = useCallback(() => {
    setEditingTitleText(articleTitle || ""); // Initialize with current title
    setIsTitleEditing(true);
  }, [articleTitle]);

  const cancelTitleEdit = useCallback(() => {
    setIsTitleEditing(false);
  }, []);

  const translationContainerRefs = useRef(new Map());

  const stableUpdateTranslationDOM = useCallback((chunkIndex, itemsContent, isActivelyTranslatingChunk) => { // Added isActivelyTranslatingChunk
    const startTime = performance.now();
    const currentItemsToRender = itemsContent || []; // Normalize to empty array if null/undefined

    const container = (typeof window !== 'undefined' && window._translationContainers && window._translationContainers[chunkIndex])
      || translationContainerRefs.current.get(chunkIndex);

    if (!container) {
      return;
    }

    if (typeof window !== 'undefined' && !window._lastTranslationItems) {
      window._lastTranslationItems = {};
    }
    const lastRenderedItems = window._lastTranslationItems ? (window._lastTranslationItems[chunkIndex] || []) : [];

    const areItemsEqual = isEqual(currentItemsToRender, lastRenderedItems);

    if (areItemsEqual) {
      const isEmptyRender = currentItemsToRender.length === 0;
      const containerHasPlaceholder = container.querySelector('[data-placeholder="true"]');
      const containerIsEmptyOrHasOnlyPlaceholder = container.childNodes.length === 0 || (container.childNodes.length === 1 && containerHasPlaceholder);

      if (isEmptyRender && containerIsEmptyOrHasOnlyPlaceholder) {
        return; // Content unchanged (empty, container already placeholder/empty)
      }
      if (!isEmptyRender && !containerIsEmptyOrHasOnlyPlaceholder) { // Both have actual content and content is same
        return; // Content unchanged (non-empty, container has content)
      }
      // If states are mixed (e.g. content same but one is placeholder and other is not, or vice-versa), proceed to normalize DOM.
    }

    // Proceed with DOM update if items are not equal or if container state mismatch
    if (currentItemsToRender.length === 0) {
      const placeholder = document.createElement('div');
      placeholder.style.minHeight = '1.5em';
      placeholder.style.opacity = '0';
      placeholder.innerHTML = '&nbsp;';
      placeholder.setAttribute('data-placeholder', 'true');

      if (container.childNodes.length !== 1 || !container.querySelector('[data-placeholder="true"]')) {
        container.innerHTML = '';
        container.appendChild(placeholder);
      }
    } else {
      const fragment = document.createDocumentFragment();
      currentItemsToRender.forEach((item, idx) => {
        // 跳过 null 或 undefined 的 item，不在正常渲染时显示
        if (item === undefined || item === null) {
          return;
        }

        if (typeof item === 'object' && item !== null) {
          const tag = item.tag || 'p';
          const elem = document.createElement(tag);
          if (item.id) elem.setAttribute('data-item-id', String(item.id));

          if (item.tag === 'img' && item.src) {
            Object.keys(item).forEach(key => {
              if (!['tag', 'id', 'children'].includes(key)) elem.setAttribute(key, item[key]);
            });
            //elem.setAttribute('loading', 'lazy');
          } else if (item.tag === 'table' && typeof item.children === 'string') {
            elem.innerHTML = item.children || ''; // Potentially unsafe
          } else if (item.children) {
            // 确保 children 是字符串，避免 [object Object] 问题
            const childrenContent = typeof item.children === 'string'
              ? item.children
              : (typeof item.children === 'object'
                ? JSON.stringify(item.children)
                : String(item.children || ''));
            elem.innerHTML = childrenContent; // 使用innerHTML以正确渲染HTML标签如<sup>、<sub>等
          }
          fragment.appendChild(elem);
        }
      });
      container.innerHTML = '';
      container.appendChild(fragment);
    }

    if (typeof window !== 'undefined') {
      window._lastTranslationItems[chunkIndex] = cloneDeep(currentItemsToRender);
    }

  }, []); // Removed translationContainerRefs from dependencies as it's a stable ref
  const {
    stoppingChunks,
    handleTranslateAll: handleTranslateAllHook,
    handleCancelTranslateAll: handleCancelTranslateAllHook,
    handleClearAll: handleClearAllHook,
    handleTranslateChunk: handleTranslateChunkHook,
    handleStopChunkTranslation: handleStopChunkTranslationHook,
    handleClearTranslatedChunk: handleClearTranslatedChunkHook,
  } = useTranslationControls(chunked, stableUpdateTranslationDOM);

  const {
    editingState,
    editText,
    // isLoading: isAIConverting, // This state is internal to useEditControls
    debouncedSetEditText: debouncedSetEditTextHook,
    handleEditStart: handleEditStartHook,
    handleEditCancel: handleEditCancelHook,
    handleEditSave: handleEditSaveHook,
  } = useEditControls(
    chunked,
    allChunkTranslatedItems,
    updateOriginalChunk, // Pass from store
    updateTranslatedChunk, // Pass from store
    // messageApi and modal are accessed via App.useApp() within the hook
    // storeParseTextPrompt, storeDefaultApiModel, storeApis are accessed via useStore.getState() within the hook
  );



  const isChunkTranslating = useMemo(() => (chunkIndex) => {
    return currentTranslationControllers.has(chunkIndex);
  }, [currentTranslationControllers]);

  const shouldDisableTranslateButton = useMemo(() => (chunkIndex) => {
    return currentTranslationControllers.has(chunkIndex);
  }, [currentTranslationControllers]);

  // updateTranslationDOM is now defined before useTranslationControls

  // 同步计算出的标题到本地状态
  useEffect(() => {
    setArticleTitle(computedTitle);
  }, [computedTitle]);

  // 同步计算出的总结到本地状态
  useEffect(() => {
    setArticleSummary(computedSummary);
  }, [computedSummary]);

  // 同步计算出的refs和citation到本地状态
  useEffect(() => {
    setRefsData(computedRefs || '');
  }, [computedRefs]);

  useEffect(() => {
    setCitationData(computedCitation || '');
  }, [computedCitation]);


  useEffect(() => {
    // Effect for concurrencyLevel if any side effects are needed
  }, [concurrencyLevel]);

  useEffect(() => {
    return () => {
      debouncedSetEditTextHook.cancel(); // Cancel from the hook
      if (debounceTimerRef.current) { // This specific ref might not be needed if hook manages its own debounce cleanup
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [debouncedSetEditTextHook]);

  const handlePreview = useCallback(() => {
    if (uuid) {
      const previewUrl = `/article/${uuid}`;
      window.open(previewUrl, '_blank', 'noopener,noreferrer');
    } else {
      messageApi.error("无法获取文章标识符以进行预览");
    }
  }, [uuid, messageApi]);

  const triggerSaveConcurrencyLevel = useCallback(async (value) => {
    const newLevel = value || 1;
    try {
      const response = await fetch(`${apiBaseUrl}api/user/concurrencyLevel`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ concurrencyLevel: newLevel })
      });
      if (!response.ok) throw new Error(`保存并发设置失败: ${response.statusText}`);
      const result = await response.json();
      if (result.success) {
        setUser({ ...user, concurrencyLevel: newLevel });
      } else {
        throw new Error(result.error || '保存并发设置时发生未知错误');
      }
    } catch (error) {
      messageApi.error(`保存并发设置失败: ${error.message}`);
    }
  }, [user, setUser, messageApi]);





  const subscribedRef = useRef(false);

  // 不再需要事件计数器

  // 添加一个ref，记录上一次渲染的props和state
  const prevPropsStateRef = useRef({
    chunked: null,
    allChunkTranslatedItems: null,
    editingState: null,
    isTranslatingAllActive: null
  });

  // 检测状态变化并记录日志
  useEffect(() => {
    const prevState = prevPropsStateRef.current;
    const currentState = {
      chunked,
      allChunkTranslatedItems,
      editingState,
      isTranslatingAllActive
    };

    // 检查哪些状态发生了变化
    const changedStates = [];
    if (prevState.chunked !== chunked) changedStates.push('chunked');
    if (prevState.allChunkTranslatedItems !== allChunkTranslatedItems) changedStates.push('allChunkTranslatedItems');
    if (prevState.editingState !== editingState) changedStates.push('editingState');
    if (prevState.isTranslatingAllActive !== isTranslatingAllActive) changedStates.push('isTranslatingAllActive');

    if (changedStates.length > 0) {
    }
    // 更新ref
    prevPropsStateRef.current = currentState;
  }, [chunked, allChunkTranslatedItems, editingState, isTranslatingAllActive, renderCount]);

  // 使用useRef存储最新的状态，避免在事件处理函数中捕获过时的状态
  const latestStateRef = useRef({
    chunked,
    stableUpdateTranslationDOM
  });

  // 每次渲染时更新ref中的状态
  useEffect(() => {
    latestStateRef.current = {
      chunked,
      stableUpdateTranslationDOM
    };
  });

  // 只在组件挂载和卸载时设置事件监听器，避免频繁重新订阅
  useEffect(() => {
    // 创建事件处理函数，使用latestStateRef获取最新状态
    const handleStreamUpdate = (event) => {
      // 从ref中获取最新状态
      const {
        stableUpdateTranslationDOM: currentUpdateDOM
      } = latestStateRef.current;

      const { chunkIndex, text } = event.detail;

      if (chunkIndex !== undefined) {
        const containerExists = window._translationContainers && window._translationContainers[chunkIndex];
        if (containerExists) {
          // Direct DOM update from stream event is disabled.
          // Updates are driven by Zustand store changes via TranslatedChunkDisplay's useEffect.
        }
      }
    };

    // 只在组件挂载时添加事件监听器
    if (!subscribedRef.current) {
      window.addEventListener('stream-translation-update', handleStreamUpdate);
      subscribedRef.current = true;
    }

    // 在组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('stream-translation-update', handleStreamUpdate);
      subscribedRef.current = false;
    };
  }, []); // 空依赖数组，只在挂载和卸载时执行

  const renderTranslatedContent = useCallback((items, translatedItems, chunkIndex) => {
    const isEditingTranslated = editingState.type === 'translated' && editingState.index === chunkIndex;
    const isTranslating = isChunkTranslating(chunkIndex);
    const hasMismatchedTagCount = checkTagCountMismatch(translatedItems, items, isTranslating);

    return (
      <>
        {/* Text Area for Editing - conditionally rendered inside a visibility-toggled div */}
        <div style={{ display: isEditingTranslated ? 'block' : 'none', width: '100%', height: '100%' }}>
          {isEditingTranslated && (
            <EnhanceTextArea
              key={`edit-area-${chunkIndex}`} // Re-key to ensure defaultValue applies on edit start
              defaultValue={editText} // editText is specific to the current editing chunk
              onChange={debouncedSetEditTextHook}
              showLineNumbers={true}
              autoSize={{ minRows: 6, maxRows: 25 }}
              style={{ width: '100%', resize: 'vertical' }}
              autoFocus
            />
          )}
        </div>

        {/* TranslatedChunkDisplay for Viewing - visibility controlled by style, always rendered */}
        <div style={{ display: !isEditingTranslated ? 'block' : 'none', width: '100%', height: '100%' }}>
          {hasMismatchedTagCount && (
            <div className="tag-count-mismatch-warning">
              <span style={{ backgroundColor: '#F5A623', color: 'white', borderRadius: '50%', width: '16px', height: '16px', display: 'inline-flex', justifyContent: 'center', alignItems: 'center', marginRight: '8px', fontSize: '12px', fontWeight: 'bold' }}>!</span>
              原文与译文段数不一致
            </div>
          )}
          <TranslatedChunkDisplay
            key={`display-chunk-${chunkIndex}`} // Stable key for the display component
            chunkIndex={chunkIndex}
            translatedItems={translatedItems}
            isEditingTranslatedFlag={false} // Always false as edit is handled by the TextArea above
            updateDOMFunc={stableUpdateTranslationDOM}
            containerRefs={translationContainerRefs}
            isActivelyTranslating={isTranslating} // Pass down the isTranslating state
          />
        </div>
      </>
    );
  }, [editingState, editText, debouncedSetEditTextHook, isChunkTranslating, stableUpdateTranslationDOM, translationContainerRefs, checkTagCountMismatch]);

  const handleDeleteChunk = useCallback(async (idx) => {
    try {
      await deleteChunkAction(chunked[idx]); // Assumes chunked[idx] is the correct identifier for deletion
      messageApi.success(`区块 ${idx} 已删除`);
    } catch (error) {
      messageApi.error(`删除区块失败: ${error.message}`);
    }
  }, [chunked, deleteChunkAction, messageApi]);

  // 停止AI总结生成
  const handleStopSummary = async () => {
    if (!isGeneratingSummary || isStoppingSummary) return;

    setIsStoppingSummary(true);

    try {
      // 中断当前的AI请求
      if (summaryAbortController) {
        summaryAbortController.abort();
      }

      // 保存当前已生成的内容
      const currentStreamingText = streamingSummaryText.trim();
      if (currentStreamingText) {
        await handleSummaryEdit(currentStreamingText);
        messageApi.success({ content: '已停止总结生成并保存当前内容', duration: 3 });
      } else {
        messageApi.success({ content: '已停止总结生成', duration: 2 });
      }
    } catch (error) {
      console.error("停止总结生成时出错:", error);
      messageApi.error(`停止总结失败: ${error.message}`);
    } finally {
      setIsStoppingSummary(false);
    }
  };

  const handleGenerateSummary = async () => { // Removed isViaProxy parameter
    if (!articleFromStore || !articleFromStore.uuid) {
      messageApi.error("文章信息不完整，无法生成总结。");
      return;
    }
    if (isGeneratingSummary) return; // Prevent multiple calls

    const allText = getAllTextFromChunks(chunked);
    if (!allText.trim()) {
      messageApi.info("文章内容为空，无法生成总结。");
      return;
    }

    // 创建新的AbortController
    const controller = new AbortController();
    setSummaryAbortController(controller);

    setIsGeneratingSummary(true);
    setStreamingSummaryText('');
    setSummaryThinkContent('');
    setIsSummaryThinking(false); // Initial state, might be set to true in stream
    setSummaryThinkCompleted(false);
    const aiMessageKey = `ai-generating-summary`; // Simplified key

    try {
      let providerName, modelName, selectedApiConfig;
      if (storeDefaultApiModel && storeDefaultApiModel[0] && storeDefaultApiModel[1]) {
        [providerName, modelName] = storeDefaultApiModel;
        if (providerName === '系统默认') {
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
          if (!selectedApiConfig.models.includes(modelName)) modelName = selectedApiConfig.models[0];
        } else {
          selectedApiConfig = storeApis.find(api => api.provider === providerName);
        }
      } else {
        providerName = '系统默认'; modelName = 'GLM-4-9B-0414';
        selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
      }

      if (!selectedApiConfig || (selectedApiConfig.key !== 'system-default' && (!selectedApiConfig.apiUrl || !selectedApiConfig.apiKey)) || (selectedApiConfig.key === 'system-default' && !selectedApiConfig.apiUrl) || !selectedApiConfig.models || !selectedApiConfig.models.includes(modelName)) {
        throw new Error(`AI模型配置不完整或无效: ${providerName} - ${modelName}`);
      }

      const baseSummaryPrompt = storeSummaryGenerationPrompt || defaultSummaryGenerationPromptText;
      const summarySystemPrompt = appendGlossaryToPrompt(baseSummaryPrompt, 'summaryGeneration');

      let finalGeneratedSummary = '';
      let currentAccumulatedReasoning = '';
      let currentHasStartedFormalContent = false;
      let summaryThinkUpdateTimeout = null;

      const batchSummaryThinkUpdate = (newThinkContent, newIsThinking, newIsCompleted) => {
        if (summaryThinkUpdateTimeout) clearTimeout(summaryThinkUpdateTimeout);
        summaryThinkUpdateTimeout = setTimeout(() => {
          if (newThinkContent !== undefined) setSummaryThinkContent(newThinkContent);
          if (newIsThinking !== undefined) setIsSummaryThinking(newIsThinking);
          if (newIsCompleted !== undefined) setSummaryThinkCompleted(newIsCompleted);
        }, 50); // 保持50ms延迟以合并快速更新
      };

      const handleStreamChunk = (chunkData) => {
        const { content, reasoning, error, isFinal } = chunkData;

        if (error) {
          console.error("Error in summary stream chunk:", error);
          return false; // Stop stream processing on error
        }

        if (isFinal) {
          if (!currentHasStartedFormalContent && currentAccumulatedReasoning) {
            // If stream ends and only reasoning was received, ensure 'thinking' phase is marked complete.
            batchSummaryThinkUpdate(currentAccumulatedReasoning, false, true); // Pass current reasoning, set thinking to false, completed to true
          } else if (currentHasStartedFormalContent) {
            // If formal content had started, ensure thinking is marked as completed if it was active.
            batchSummaryThinkUpdate(summaryThinkContent, false, true); // Keep existing think content, set thinking to false, completed to true
          }
          if (summaryThinkUpdateTimeout) clearTimeout(summaryThinkUpdateTimeout); // Clear any pending timeout
          return; // Important: Let streamTextWithAI know processing is done for this path.
        }

        if (reasoning && String(reasoning).trim() !== "") {
          currentAccumulatedReasoning += reasoning;
          batchSummaryThinkUpdate(currentAccumulatedReasoning, true, undefined); // Update think content, set thinking to true
        }

        if (content !== null && content !== undefined){
          if (!currentHasStartedFormalContent && currentAccumulatedReasoning) {
            // This is the first piece of 'formal' content after some 'reasoning'
            batchSummaryThinkUpdate(currentAccumulatedReasoning, false, true); // Finalize current reasoning, set thinking to false, completed to true
            currentHasStartedFormalContent = true;
          } else if (!currentHasStartedFormalContent) {
            // This is the first piece of 'formal' content, and no prior 'reasoning' was processed in this stream.
            currentHasStartedFormalContent = true;
          }

          finalGeneratedSummary += content;
          setStreamingSummaryText(finalGeneratedSummary); // Update summary text
        }

        return true; // Continue stream processing
      };

      const streamResult = await streamTextWithAI(
        allText,
        summarySystemPrompt,
        selectedApiConfig,
        modelName,
        controller.signal,
        handleStreamChunk,
        false,
        { temperature: 0.7 }
      );

      if (!streamResult.success) {
        throw new Error(streamResult.error || 'AI 生成总结流式处理失败 (via streamTextWithAI)');
      }
      
      if (summaryThinkUpdateTimeout) clearTimeout(summaryThinkUpdateTimeout);
      // Always attempt to finalize think update if formal content never started
      if (!currentHasStartedFormalContent) {
          batchSummaryThinkUpdate(undefined, false, true);
      }
      // Ensure thinking state is finalized after stream completion
      setIsSummaryThinking(false);
      setSummaryThinkCompleted(true);

      if (finalGeneratedSummary.trim()) {
        await handleSummaryEdit(finalGeneratedSummary.trim());
        messageApi.success({ content: 'AI已生成新总结并保存', key: aiMessageKey, duration: 3 });
      } else {
        throw new Error('AI返回的总结为空或格式不正确 (after stream)');
      }

    } catch (error) {
      console.error("Error generating summary with AI (Refactored):", error);
      messageApi.error({ content: `AI生成总结失败: ${error.message || '未知错误'}`, key: aiMessageKey, duration: 5 });
      // Always ensure thinking state is finalized on error
      setIsSummaryThinking(false);
      setSummaryThinkCompleted(true);
    } finally {
      setIsGeneratingSummary(false);
      setSummaryAbortController(null);
      // setStreamingSummaryText(''); // Optional: clear streaming text on finish/error
    }
  };

  const handleGenerateTitle = async () => { // Removed isViaProxy parameter
    if (!articleFromStore || !articleFromStore.uuid) {
      messageApi.error("文章信息不完整，无法生成标题。");
      return;
    }
    if (isGeneratingTitle) return; // Prevent multiple calls

    const allText = getAllTextFromChunks(chunked);
    if (!allText.trim()) {
      messageApi.info("文章内容为空，无法生成标题。");
      return;
    }
    setIsGeneratingTitle(true);
    const aiMessageKey = `ai-generating-title`; // Simplified key

    try {
      let providerName, modelName, selectedApiConfig;
      if (storeDefaultApiModel && storeDefaultApiModel[0] && storeDefaultApiModel[1]) {
        [providerName, modelName] = storeDefaultApiModel;
        if (providerName === '系统默认') {
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
          if (!selectedApiConfig.models.includes(modelName)) modelName = selectedApiConfig.models[0];
        } else {
          selectedApiConfig = storeApis.find(api => api.provider === providerName);
        }
      } else {
        providerName = '系统默认'; modelName = 'GLM-4-9B-0414';
        selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
      }

      if (!selectedApiConfig || (selectedApiConfig.key !== 'system-default' && (!selectedApiConfig.apiUrl || !selectedApiConfig.apiKey)) || (selectedApiConfig.key === 'system-default' && !selectedApiConfig.apiUrl) || !selectedApiConfig.models || !selectedApiConfig.models.includes(modelName)) {
        throw new Error(`AI模型配置不完整或无效: ${providerName} - ${modelName}`);
      }

      const baseTitlePrompt = storeTitleGenerationPrompt || defaultTitleGenerationPromptText;
      const titleSystemPrompt = appendGlossaryToPrompt(baseTitlePrompt, 'titleGeneration');
      const userPromptTemplate = "\${text}"; // Text itself will be the user message content

      const aiResult = await formatTextWithAI(
        allText,
        titleSystemPrompt,
        selectedApiConfig,
        modelName,
        null, // signal - AbortSignal can be added if cancellation is needed
        1,    // concurrencyLevel - use default of 1 as chunking is disabled
        true  // disableChunking - set to true for title generation
      );

      if (aiResult.error) {
        throw new Error(aiResult.error); // Throw the specific error from AI utility
      }

      const generatedTitle = aiResult.formattedText?.trim();

      if (generatedTitle) {
        await handleTitleEdit(generatedTitle);
        messageApi.success({ content: 'AI已生成新标题并保存', key: aiMessageKey, duration: 3 });
      } else {
        throw new Error('AI返回的标题为空或格式不正确 (via formatTextWithAI)');
      }
    } catch (error) {
      console.error("Error generating title with AI:", error);
      messageApi.error({ content: `AI生成标题失败: ${error.message || '未知错误'}`, key: aiMessageKey, duration: 5 });
    } finally {
      setIsGeneratingTitle(false);
    }
  };

  // 总结编辑相关函数
  const handleSummaryEdit = useCallback(async (newSummary) => {
    const trimmedSummary = typeof newSummary === 'string' ? newSummary.trim() : '';
    setArticleSummary(trimmedSummary || null);

    const currentArticleFromStore = useStore.getState().article;

    if (currentArticleFromStore && currentArticleFromStore.uuid) {
      try {
        await saveArticle({
          summary: trimmedSummary || null
        });
      } catch (error) {
        messageApi.error(`保存总结失败: ${error.message}`);
      }
    }
  }, [saveArticle, messageApi]);

  const handleSaveSummaryEdit = useCallback(() => {
    if (typeof editingSummaryText === 'string') {
      handleSummaryEdit(editingSummaryText.trim());
    }
    setIsSummaryEditing(false);
  }, [editingSummaryText, handleSummaryEdit]);

  const startSummaryEdit = useCallback(() => {
    setEditingSummaryText(articleSummary || "");
    setIsSummaryEditing(true);
  }, [articleSummary]);

  const cancelSummaryEdit = useCallback(() => {
    setEditingSummaryText(articleSummary || ""); // 恢复原始内容
    setIsSummaryEditing(false);
  }, [articleSummary]);

  const handleClearSummary = useCallback(async () => {
    await handleSummaryEdit(null);
    messageApi.success('总结已清空');
  }, [handleSummaryEdit, messageApi]);

  // 参考相关函数
  const startRefsEdit = useCallback(() => {
    setEditingRefsText(refsData || ''); // 初始化为存储的数据
    setIsRefsEditing(true);
  }, [refsData]);

  const cancelRefsEdit = useCallback(() => {
    setIsRefsEditing(false);
    setEditingRefsText('');
  }, []);

  const handleSaveRefs = useCallback(async () => {
    try {
      await saveArticle({
        refs: editingRefsText
      });

      setIsRefsEditing(false);
      setRefsData(editingRefsText); // 更新本地存储的数据

      // 更新store中的article数据
      const currentArticle = useStore.getState().article;
      if (currentArticle) {
        useStore.setState({
          article: {
            ...currentArticle,
            refs: editingRefsText
          }
        });
      }

      messageApi.success('参考已保存');
    } catch (error) {
      console.error('保存参考失败:', error);
      messageApi.error(`保存参考失败: ${error.message || '未知错误'}`);
    }
  }, [uuid, editingRefsText, messageApi]);

  const handleParseRefs = useCallback(async () => { // isViaProxy parameter removed
    if (!editingRefsText.trim()) {
      messageApi.warning('请先输入参考内容');
      return;
    }

    if (isParsingRefs) return; // Simpler check as formatTextWithAI handles proxy internally

    setIsParsingRefs(true);
    const aiMessageKey = `ai-parsing-reference`; // Simplified key

    try {
      messageApi.loading({ content: 'AI正在解析参考文献...', key: aiMessageKey, duration: 0 });

      let selectedApiConfig;
      let modelName;

      if (storeDefaultApiModel && storeDefaultApiModel[0] && storeDefaultApiModel[1]) {
        const [provider, model] = storeDefaultApiModel;
        if (provider === '系统默认') {
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: 'INTERNAL_FRONTEND_CALL', models: [model] };
          modelName = model;
          if (selectedApiConfig.models && !selectedApiConfig.models.includes(modelName) && selectedApiConfig.models.length > 0) {
            modelName = selectedApiConfig.models[0];
          } else if (selectedApiConfig.models && selectedApiConfig.models.length === 0) {
            modelName = 'GLM-4-9B-0414'; 
            selectedApiConfig.models = [modelName];
          }
        } else {
          selectedApiConfig = storeApis.find(api => api.provider === provider);
          modelName = model;
        }
      } else { 
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: 'INTERNAL_FRONTEND_CALL', models: ['GLM-4-9B-0414'] };
          modelName = 'GLM-4-9B-0414';
      }

      if (!selectedApiConfig || !modelName || !selectedApiConfig.models || !selectedApiConfig.models.includes(modelName)) {
        console.error('Invalid API config or modelName:', { provider: selectedApiConfig?.provider, modelName, availableModels: selectedApiConfig?.models });
        throw new Error(`AI模型配置不完整或无效: ${selectedApiConfig?.provider || '未知Provider'} - ${modelName || '未知Model'}`);
      }

      const systemPrompt = storeReferenceParsePrompt || defaultReferenceParsePromptText;
      const userPromptTemplateForRefs = "\${text}"; // Standard user prompt template

      // Call the shared utility function formatTextWithAI
      const aiResult = await formatTextWithAI(
        editingRefsText,
        systemPrompt,
        selectedApiConfig,
        modelName,
        null // signal - AbortSignal can be added if cancellation is needed
        // userPromptTemplateForRefs is not directly passed; text itself is the user content.
        // isViaProxyInternal and options are not params of the current formatTextWithAI signature.
      );

      if (aiResult.error) {
        throw new Error(aiResult.error); // Throw the specific error from AI utility
      }

      const formattedResultText = aiResult.formattedText; // Use formattedText

      if (formattedResultText) {
        setEditingRefsText(formattedResultText);
        messageApi.success({ content: 'AI已完成参考解析', key: aiMessageKey, duration: 3 });
      } else {
        throw new Error('AI返回的解析结果为空或格式不正确 (via formatTextWithAI)');
      }
    } catch (error) {
      console.error("Error parsing reference with AI:", error);
      messageApi.error({ content: `AI解析参考失败: ${error.message || '未知错误'}`, key: aiMessageKey, duration: 5 });
    } finally {
      setIsParsingRefs(false);
    }
  }, [editingRefsText, isParsingRefs, storeReferenceParsePrompt, messageApi, defaultReferenceParsePromptText, storeApis, storeDefaultApiModel]);

  // 来源相关函数
  const startCitationEdit = useCallback(() => {
    setEditingCitationText(citationData || ''); // 初始化为存储的数据
    setIsCitationEditing(true);
  }, [citationData]);

  const cancelCitationEdit = useCallback(() => {
    setIsCitationEditing(false);
    setEditingCitationText('');
  }, []);

  const handleSaveCitation = useCallback(async () => {
    try {
      await saveArticle({
        citation: editingCitationText
      });

      setIsCitationEditing(false);
      setCitationData(editingCitationText); // 更新本地存储的数据

      // 更新store中的article数据
      const currentArticle = useStore.getState().article;
      if (currentArticle) {
        useStore.setState({
          article: {
            ...currentArticle,
            citation: editingCitationText
          }
        });
      }

      messageApi.success('来源已保存');
    } catch (error) {
      console.error('保存来源失败:', error);
      messageApi.error(`保存来源失败: ${error.message || '未知错误'}`);
    }
  }, [uuid, editingCitationText, messageApi]);

  const handleClearCitation = useCallback(async () => {
    try {
      await saveArticle({
        citation: null
      });

      setCitationData(''); // 更新本地存储的数据

      // 更新store中的article数据
      const currentArticle = useStore.getState().article;
      if (currentArticle) {
        useStore.setState({
          article: {
            ...currentArticle,
            citation: null
          }
        });
      }

      messageApi.success('来源已清空');
    } catch (error) {
      console.error('清空来源失败:', error);
      messageApi.error(`清空来源失败: ${error.message || '未知错误'}`);
    }
  }, [saveArticle, messageApi]);

  const handleClearRefs = useCallback(async () => {
    try {
      await saveArticle({
        refs: null
      });

      setRefsData(''); // 更新本地存储的数据

      // 更新store中的article数据
      const currentArticle = useStore.getState().article;
      if (currentArticle) {
        useStore.setState({
          article: {
            ...currentArticle,
            refs: null
          }
        });
      }

      messageApi.success('参考已清空');
    } catch (error) {
      console.error('清空参考失败:', error);
      messageApi.error(`清空参考失败: ${error.message || '未知错误'}`);
    }
  }, [saveArticle, messageApi]);



  // 全文编辑相关函数 - 统一的文本生成逻辑
  const getFullText = useCallback((isTranslated = false) => {
    const sourceData = isTranslated ? allChunkTranslatedItems : chunked;
    if (!sourceData || sourceData.length === 0) return '';

    const lines = [];

    sourceData.forEach((chunk) => {
      if (!chunk || !Array.isArray(chunk)) return;

      chunk.forEach((item) => {
        // 在编辑模式下显示null/undefined占位符，统一显示为{null}
        if (item === null || item === undefined) {
          lines.push('{null}');
          return;
        }
        if (!item || typeof item !== 'object') {
          // 跳过无效item，但不添加行
          return;
        }

        let lineContent = '';
        if (item.tag === 'img') {
          lineContent = `<img src="${item.src}" alt="${item.alt || ''}" />`;
        } else if (item.tag === 'table' && item.children) {
          lineContent = `<table>${item.children}</table>`;
        } else if (item.children) {
          lineContent = `<${item.tag}>${item.children}</${item.tag}>`;
        } else {
          lineContent = `<${item.tag} />`;
        }

        lines.push(lineContent);
      });
    });

    return lines.join('\n');
  }, [chunked, allChunkTranslatedItems]);

  // 原文全文获取函数
  const getFullOriginalText = useCallback(() => {
    return getFullText(false);
  }, [getFullText]);

  // 译文全文获取函数
  const getFullTranslatedText = useCallback(() => {
    return getFullText(true);
  }, [getFullText]);

  // 获取chunk的关键词（用于验证定位准确性）
  const getChunkKeywords = useCallback((chunkIndex, isTranslated = false) => {
    const targetChunks = isTranslated ? allChunkTranslatedItems : chunked;
    if (!targetChunks || chunkIndex >= targetChunks.length) return '';

    const chunk = targetChunks[chunkIndex];
    if (!chunk || !Array.isArray(chunk)) return '';

    // 提取chunk中的文本内容作为关键词
    const texts = chunk
      .filter(item => item && typeof item === 'object' && item.children)
      .map(item => {
        if (typeof item.children === 'string') {
          // 移除HTML标签，只保留纯文本
          return item.children.replace(/<[^>]*>/g, '').trim();
        }
        return '';
      })
      .filter(text => text.length > 0);

    // 取前50个字符作为关键词
    const keywords = texts.join(' ').substring(0, 50);
    return keywords;
  }, [chunked, allChunkTranslatedItems]);

  // 获取当前可视区域的chunk索引
  const getCurrentVisibleChunkIndex = useCallback(() => {
    const chunksContainer = document.querySelector('.t-content');
    if (!chunksContainer) {
      return { index: 0, keywords: '' };
    }

    const chunkElements = chunksContainer.querySelectorAll('.chunk-row');
    if (chunkElements.length === 0) {
      return { index: 0, keywords: '' };
    }

    // 获取工具栏高度，调整视口计算
    const toolbar = document.querySelector('.article-controls');
    const toolbarHeight = toolbar ? toolbar.getBoundingClientRect().height : 80;

    // 计算真实的视口区域（排除工具栏）
    const viewportTop = toolbarHeight; // 视口顶部（相对于页面顶部）
    const viewportBottom = window.innerHeight; // 视口底部
    const viewportCenter = (viewportTop + viewportBottom) / 2; // 视口中心



    // 找到最接近视口中心的chunk，优先选择在视口内的chunk
    let closestChunkIndex = 0;
    let minDistance = Infinity;
    let bestVisibleChunk = -1;
    let chunkDetails = [];

    chunkElements.forEach((element, index) => {
      const rect = element.getBoundingClientRect();
      const elementTop = rect.top;
      const elementBottom = rect.bottom;
      const elementCenter = (rect.top + rect.bottom) / 2;

      // 检查chunk是否在视口内（使用getBoundingClientRect的坐标）
      const isVisible = elementBottom > viewportTop && elementTop < viewportBottom;
      const distance = Math.abs(elementCenter - viewportCenter);

      // 计算chunk在视口中的可见比例
      const visibleTop = Math.max(elementTop, viewportTop);
      const visibleBottom = Math.min(elementBottom, viewportBottom);
      const visibleHeight = Math.max(0, visibleBottom - visibleTop);
      const totalHeight = elementBottom - elementTop;
      const visibilityRatio = totalHeight > 0 ? visibleHeight / totalHeight : 0;

      // 获取chunk的第一个内容用于调试
      const chunkContent = chunked[index];
      let firstContent = '';
      if (chunkContent && Array.isArray(chunkContent) && chunkContent.length > 0) {
        const firstItem = chunkContent[0];
        if (firstItem && typeof firstItem === 'object' && firstItem.children) {
          firstContent = firstItem.children.substring(0, 50);
        } else if (firstItem && typeof firstItem === 'object' && firstItem.tag) {
          firstContent = `<${firstItem.tag}>`;
        }
      }

      chunkDetails.push({
        index,
        elementTop,
        elementBottom,
        elementCenter,
        isVisible,
        distance,
        visibilityRatio,
        rectTop: rect.top,
        rectBottom: rect.bottom,
        rectHeight: rect.height,
        firstContent,
        // 添加更多调试信息
        elementOffsetTop: element.offsetTop,
        elementScrollTop: element.scrollTop,
        elementClientTop: element.clientTop
      });

      // 优先选择在视口内且可见比例最高的chunk
      if (isVisible) {
        if (bestVisibleChunk === -1) {
          bestVisibleChunk = index;
          minDistance = distance;
        } else {
          // 如果当前chunk的可见比例更高，或者可见比例相同但距离更近，则选择当前chunk
          const currentBestDetails = chunkDetails.find(d => d.index === bestVisibleChunk);
          if (visibilityRatio > currentBestDetails.visibilityRatio ||
              (visibilityRatio === currentBestDetails.visibilityRatio && distance < minDistance)) {
            bestVisibleChunk = index;
            minDistance = distance;
          }
        }
      }

      // 记录最接近中心的chunk（作为备选）
      if (distance < minDistance && bestVisibleChunk === -1) {
        closestChunkIndex = index;
      }
    });

    // 优先返回可视的chunk，如果没有则返回最接近的
    const finalIndex = bestVisibleChunk !== -1 ? bestVisibleChunk : closestChunkIndex;
    const keywords = getChunkKeywords(finalIndex, false); // 获取原文关键词



    return { index: finalIndex, keywords };
  }, [getChunkKeywords]);

  // 计算chunk在全文中的行号位置，并返回验证信息
  const getChunkLinePosition = useCallback((chunkIndex, isTranslated = false) => {
    if (!chunked || chunkIndex >= chunked.length) return { line: 1, keywords: '', chunkText: '' };

    const targetChunks = isTranslated ? allChunkTranslatedItems : chunked;
    const fullTextProvider = isTranslated ? getFullTranslatedText : getFullOriginalText;
    const fullText = fullTextProvider();
    const lines = fullText.split('\n');

    console.log(`[getChunkLinePosition] Called with chunkIndex: ${chunkIndex}, isTranslated: ${isTranslated}`);
    console.log(`[getChunkLinePosition] Total lines in full text: ${lines.length}`);
    console.log(`[getChunkLinePosition] Total chunks: ${targetChunks.length}`);

    // 使用与getFullOriginalText/getFullTranslatedText完全相同的逻辑来计算行数
    let lineCount = 0;
    let chunkDetails = [];

    // 计算目标chunk之前的所有行数，使用与getFullOriginalText完全相同的逻辑
    for (let i = 0; i < chunkIndex && i < targetChunks.length; i++) {
      const chunk = targetChunks[i];
      let chunkLineCount = 0;
      let chunkValidItemCount = 0;

      if (chunk && Array.isArray(chunk)) {
        chunk.forEach((item, itemIndex) => {
          // 与getFullOriginalText保持完全一致的逻辑
          if (item === null || item === undefined) {
            lineCount++;
            chunkLineCount++;
            chunkValidItemCount++;
          } else if (!item || typeof item !== 'object') {
            // 跳过无效item，不增加行数（与getFullOriginalText一致）
          } else {
            // 有效item，增加行数
            lineCount++;
            chunkLineCount++;
            chunkValidItemCount++;
          }
        });
      }

      chunkDetails.push({
        chunkIndex: i,
        chunkLength: chunk?.length || 0,
        chunkLineCount,
        chunkValidItemCount,
        totalLinesSoFar: lineCount
      });

      console.log(`[getChunkLinePosition] Chunk ${i}, items: ${chunk?.length}, counted lines: ${chunkLineCount}, total lines so far: ${lineCount}`);
    }

    // 行号从1开始，所以加1
    const targetLine = lineCount + 1;
    console.log(`[getChunkLinePosition] Target chunk ${chunkIndex} should be at line: ${targetLine}`);

    // 获取目标chunk的关键词用于验证
    const keywords = getChunkKeywords(chunkIndex, isTranslated);

    // 获取目标chunk的完整文本用于验证
    const targetChunk = targetChunks[chunkIndex];
    let chunkText = '';
    let targetChunkDetails = null;
    if (targetChunk && Array.isArray(targetChunk)) {
      const validItems = [];
      targetChunk.forEach((item, itemIndex) => {
        if (item === null || item === undefined) {
          validItems.push('{null}');
        } else if (!item || typeof item !== 'object') {
          // 跳过无效item
        } else {
          if (item.tag === 'img') {
            validItems.push(`<img src="${item.src}" alt="${item.alt || ''}" />`);
          } else if (item.tag === 'table' && item.children) {
            validItems.push(`<table>${item.children}</table>`);
          } else if (item.children) {
            validItems.push(`<${item.tag}>${item.children}</${item.tag}>`);
          } else {
            validItems.push(`<${item.tag} />`);
          }
        }
      });

      chunkText = validItems.join('\n');

      targetChunkDetails = {
        chunkIndex,
        chunkLength: targetChunk.length,
        validItemCount: validItems.length,
        firstItemPreview: targetChunk[0] ? (targetChunk[0].children || targetChunk[0].tag || 'null') : 'empty',
        lastItemPreview: targetChunk[targetChunk.length - 1] ? (targetChunk[targetChunk.length - 1].children || targetChunk[targetChunk.length - 1].tag || 'null') : 'empty'
      };
    }



    return { line: targetLine, keywords, chunkText };
  }, [chunked, allChunkTranslatedItems, getChunkKeywords, getFullOriginalText, getFullTranslatedText]);

  // 新增：从 CodeMirror 行号获取 chunk 索引
  const getChunkIndexFromLineNumber = useCallback((lineNumber, isTranslated) => {
    const fullTextProvider = isTranslated ? getFullTranslatedText : getFullOriginalText;
    const fullText = fullTextProvider();
    const lines = fullText.split('\n');
    const targetChunks = isTranslated ? allChunkTranslatedItems : chunked;

    console.log(`[getChunkIndexFromLineNumber] Called with lineNumber: ${lineNumber}, isTranslated: ${isTranslated}`);
    console.log(`[getChunkIndexFromLineNumber] Total lines in full text: ${lines.length}`);
    console.log(`[getChunkIndexFromLineNumber] Total chunks: ${targetChunks.length}`);

    if (lineNumber <= 0) {
        console.warn('[getChunkIndexFromLineNumber] lineNumber is <= 0, returning 0');
        return 0;
    }

    let currentLineCount = 0;
    for (let i = 0; i < targetChunks.length; i++) {
      const chunk = targetChunks[i];
      let chunkLineCount = 0;
      if (chunk && Array.isArray(chunk)) {
        chunk.forEach(item => {
          // 保持与 getFullOriginalText/getFullTranslatedText 一致的行计数逻辑
          if (item === null || item === undefined) {
            chunkLineCount++;
          } else if (!item || typeof item !== 'object') {
            // 跳过
          } else {
            chunkLineCount++;
          }
        });
      }
      
      console.log(`[getChunkIndexFromLineNumber] Chunk ${i}, items: ${chunk?.length}, counted lines in this chunk: ${chunkLineCount}, currentTotalLines: ${currentLineCount + chunkLineCount}`);

      if (currentLineCount + chunkLineCount >= lineNumber) {
        console.log(`[getChunkIndexFromLineNumber] Found target chunk: ${i} (lines ${currentLineCount + 1} to ${currentLineCount + chunkLineCount} in full text) for editor line ${lineNumber}`);

        // 验证反向计算：从chunk索引计算回行号，看是否匹配
        const reverseCalculatedLine = currentLineCount + 1; // 这个chunk的起始行号
        console.log(`[getChunkIndexFromLineNumber] Reverse verification: chunk ${i} starts at line ${reverseCalculatedLine}, editor line was ${lineNumber}`);

        return i;
      }
      currentLineCount += chunkLineCount;
    }

    console.warn(`[getChunkIndexFromLineNumber] LineNumber ${lineNumber} exceeds total lines (${currentLineCount}) or chunks. Returning last chunk index: ${Math.max(0, targetChunks.length - 1)}`);
    return Math.max(0, targetChunks.length - 1); // 如果行号超出，返回最后一个chunk的索引
  }, [chunked, allChunkTranslatedItems, getFullOriginalText, getFullTranslatedText]);


  // 原文合并编辑相关函数
  const handleStartMergedOriginalEdit = useCallback(() => {
    const mergedText = getFullOriginalText();
    const visibleChunkInfo = getCurrentVisibleChunkIndex();
    const positionInfo = getChunkLinePosition(visibleChunkInfo.index, false);

    // 保存原始的chunk索引，用于取消时的精确定位
    setOriginalEditingChunkIndex(visibleChunkInfo.index);

    setMergedOriginalText(mergedText);
    setIsMergedOriginalEditing(true);

    // 显示定位提示，包含关键词验证
    const keywords = positionInfo.keywords || visibleChunkInfo.keywords;
    // messageApi.info(
    //   `正在定位到第 ${visibleChunkInfo.index + 1} 个区块（第 ${positionInfo.line} 行）${keywords ? `，内容：${keywords}` : ''}...`,
    //   3
    // );

    // 延迟执行定位，确保组件已渲染
    // 当当前可视位置为第1-5行时，无需滚动到目标行
    if (positionInfo.line > 5) {
      setTimeout(() => {
        const event = new CustomEvent('scrollToLine', {
          detail: {
            line: positionInfo.line,
            type: 'original',
            keywords,
            chunkIndex: visibleChunkInfo.index
          }
        });

        window.dispatchEvent(event);
      }, 100);
    }
  }, [getFullOriginalText, chunked, getCurrentVisibleChunkIndex, getChunkLinePosition, messageApi]);

  // 用于存储待滚动的目标信息
  const [pendingScrollTarget, setPendingScrollTarget] = useState(null);
  // 用于存储进入全文编辑时的原始chunk索引
  const [originalEditingChunkIndex, setOriginalEditingChunkIndex] = useState(null);

  // 通用的位置计算和滚动设置函数
  const handlePositionCalculationAndScroll = useCallback((operation, isTranslated, originalChunkIndex, setPendingTarget, clearStates) => {
    console.log(`[Debug] handlePositionCalculationAndScroll: ${operation} for ${isTranslated ? 'translated' : 'original'}`);

    // 使用统一的位置计算逻辑
    const { targetChunkIndex, capturedLineNumber, usedCurrentPosition } = calculateTargetPosition({
      operation,
      isTranslated,
      fullPageEditorRef,
      getChunkIndexFromLineNumber,
      originalEditingChunkIndex: originalChunkIndex
    });

    // 设置待滚动目标
    setPendingTarget(createScrollTarget(
      targetChunkIndex,
      capturedLineNumber,
      usedCurrentPosition
    ));

    // 执行状态清理
    if (clearStates) {
      clearStates();
    }
  }, [calculateTargetPosition, getChunkIndexFromLineNumber, createScrollTarget, fullPageEditorRef]);

  // 监听编辑状态变化，在DOM更新后执行滚动
  useEffect(() => {
    if (!isMergedOriginalEditing && pendingScrollTarget) {
      console.log('[Debug] useEffect triggered for original edit scroll:', pendingScrollTarget);

      // 使用智能滚动Hook执行滚动
      executeSmartScroll({
        ...pendingScrollTarget,
        type: 'original'
      });

      // 清除待处理的滚动目标
      setPendingScrollTarget(null);
    }
  }, [isMergedOriginalEditing, pendingScrollTarget, executeSmartScroll]);

  const handleCancelMergedOriginalEdit = useCallback(() => {
    console.log('[Debug] handleCancelMergedOriginalEdit: Start');

    // 使用通用函数处理位置计算和滚动设置
    handlePositionCalculationAndScroll(
      'save', // 改为 'save' 以获取当前focus位置，而不是原始位置
      false, // isTranslated
      originalEditingChunkIndex,
      setPendingScrollTarget,
      () => {
        // 状态清理函数
        setIsMergedOriginalEditing(false);
        setMergedOriginalText('');
        setOriginalEditingChunkIndex(null);
      }
    );
  }, [originalEditingChunkIndex, handlePositionCalculationAndScroll]);

  const handleSaveMergedOriginalEdit = useCallback(async () => {
    console.log('[Debug] handleSaveMergedOriginalEdit: Start');
    console.log('[Debug] handleSaveMergedOriginalEdit: Original editing chunk index:', originalEditingChunkIndex);

    try {
      let cleanedHtmlString = '';
      if (mergedOriginalText && mergedOriginalText.trim() !== '') {
        const tempDiv = $('<div></div>').html(mergedOriginalText.trim());
        tempDiv.children().each((_, element) => {
          cleanedHtmlString += element.outerHTML;
        });
      }

      // 使用清理后的 HTML 字符串进行解析
      const parsedOriginal = html2content(cleanedHtmlString);

      if (parsedOriginal && parsedOriginal.length > 0) {
        setContent(parsedOriginal);

        // 保存到后端数据库
        const currentArticleFromStore = useStore.getState().article;

        if (currentArticleFromStore && currentArticleFromStore.uuid) {
          await saveArticle({
            content: parsedOriginal
          });
          messageApi.success('原文合并编辑已保存');

          // 使用通用函数处理位置计算和滚动设置
          handlePositionCalculationAndScroll(
            'save',
            false, // isTranslated
            originalEditingChunkIndex,
            setPendingScrollTarget,
            () => {
              // 状态清理函数
              setIsMergedOriginalEditing(false);
              setMergedOriginalText('');
              setOriginalEditingChunkIndex(null);
            }
          );
        } else {
          messageApi.error('无法保存：缺少文章信息');
        }
      } else {
        messageApi.warning('解析原文内容失败，请检查格式。提示：请确保内容为有效的HTML格式，或输入纯文本让系统自动转换。');
      }
    } catch (error) {
      console.error('保存原文合并编辑失败:', error);
      messageApi.error(`保存失败: ${error.message || '未知错误'}`);
    }
  }, [mergedOriginalText, setContent, saveArticle, messageApi, originalEditingChunkIndex, handlePositionCalculationAndScroll]);

  // 译文合并编辑相关函数
  const handleStartMergedTranslatedEdit = useCallback(() => {
    const mergedText = getFullTranslatedText();
    const visibleChunkInfo = getCurrentVisibleChunkIndex();
    const positionInfo = getChunkLinePosition(visibleChunkInfo.index, true);

    // 保存原始的chunk索引，用于取消时的精确定位
    setOriginalTranslatedEditingChunkIndex(visibleChunkInfo.index);

    setMergedTranslatedText(mergedText);
    setIsMergedTranslatedEditing(true);

    // 显示定位提示，包含关键词验证
    const keywords = positionInfo.keywords || getChunkKeywords(visibleChunkInfo.index, true);
    // messageApi.info(
    //   `正在定位到第 ${visibleChunkInfo.index + 1} 个区块（第 ${positionInfo.line} 行）${keywords ? `，内容：${keywords}` : ''}...`,
    //   3
    // );

    // 延迟执行定位，确保组件已渲染
    // 当当前可视位置为第1-5行时，无需滚动到目标行
    if (positionInfo.line > 5) {
      setTimeout(() => {
        const event = new CustomEvent('scrollToLine', {
          detail: {
            line: positionInfo.line,
            type: 'translated',
            keywords,
            chunkIndex: visibleChunkInfo.index
          }
        });
        window.dispatchEvent(event);
      }, 100);
    }
  }, [getFullTranslatedText, getCurrentVisibleChunkIndex, getChunkLinePosition, getChunkKeywords, messageApi]);

  // 用于存储译文编辑待滚动的目标信息
  const [pendingTranslatedScrollTarget, setPendingTranslatedScrollTarget] = useState(null);
  // 用于存储进入译文编辑时的原始chunk索引
  const [originalTranslatedEditingChunkIndex, setOriginalTranslatedEditingChunkIndex] = useState(null);

  // 监听译文编辑状态变化，在DOM更新后执行滚动
  useEffect(() => {
    if (!isMergedTranslatedEditing && pendingTranslatedScrollTarget) {
      console.log('[Debug] useEffect triggered for translated edit scroll:', pendingTranslatedScrollTarget);

      // 使用智能滚动Hook执行滚动
      executeSmartScroll({
        ...pendingTranslatedScrollTarget,
        type: 'translated'
      });

      // 清除待处理的滚动目标
      setPendingTranslatedScrollTarget(null);
    }
  }, [isMergedTranslatedEditing, pendingTranslatedScrollTarget, executeSmartScroll]);

  const handleCancelMergedTranslatedEdit = useCallback(() => {
    console.log('[Debug] handleCancelMergedTranslatedEdit: Start');

    // 使用通用函数处理位置计算和滚动设置
    handlePositionCalculationAndScroll(
      'save', // 改为 'save' 以获取当前focus位置，而不是原始位置
      true, // isTranslated
      originalTranslatedEditingChunkIndex,
      setPendingTranslatedScrollTarget,
      () => {
        // 状态清理函数
        setIsMergedTranslatedEditing(false);
        setMergedTranslatedText('');
        setOriginalTranslatedEditingChunkIndex(null);
      }
    );
  }, [originalTranslatedEditingChunkIndex, handlePositionCalculationAndScroll]);

  const handleSaveMergedTranslatedEdit = useCallback(async () => {
    console.log('[Debug] handleSaveMergedTranslatedEdit: Start');
    console.log('[Debug] handleSaveMergedTranslatedEdit: Original editing chunk index:', originalTranslatedEditingChunkIndex);

    try {
      const preprocessedHtml = mergedTranslatedText
        .split('\n')
        .map(line => {
          const trimmedLine = line.trim();
          // 不要包装 {null} 占位符
          if (trimmedLine === '{null}') {
            return trimmedLine;
          }
          if (trimmedLine && !trimmedLine.startsWith('<') && !trimmedLine.endsWith('>')) {
            return `<p>${trimmedLine}</p>`;
          }
          return line;
        })
        .join('\n');

      let parsedTranslated;
      let conversionOk = true;

      try {
        parsedTranslated = html2content(preprocessedHtml);

        if (preprocessedHtml.trim() !== '' && (!parsedTranslated || parsedTranslated.length === 0)) {
          conversionOk = false;
        }
      } catch (e) {
        console.error("[handleSaveMergedTranslatedEdit] html2content转换错误:", e);
        conversionOk = false;
      }

      if (conversionOk && parsedTranslated) {

        // Roo: 彻底重构 handleSaveMergedTranslatedEdit 的核心逻辑
        const { content: originalContent } = useStore.getState();
        const newTranslatedObject = {};

        // 检查解析后的翻译内容和原文内容的长度是否匹配
        if (parsedTranslated.length !== originalContent.length) {
          messageApi.warning('译文段落数与原文不匹配，可能导致保存内容错位。');
        }

        // 遍历原文内容，用其ID作为key，保证与后端的兼容性
        originalContent.forEach((originalItem, index) => {
          if (originalItem && typeof originalItem.id !== 'undefined') {
            const newTranslatedItem = parsedTranslated[index];
            if (newTranslatedItem) {
              // 将解析出的新内容，赋予和原文匹配的ID
              newTranslatedObject[originalItem.id] = { ...newTranslatedItem, id: originalItem.id };
            } else {
              // 如果译文比原文短，则将多余的原文对应的翻译设置为空
              newTranslatedObject[originalItem.id] = undefined;
            }
          }
        });

        // 对于那些在原文中不存在但在旧译文中存在的ID，可以选择保留或删除
        // 当前逻辑是完全基于原文结构重建，所以旧译文中多余的ID会被自动丢弃

        // 3. 一次性更新 Zustand store，这会触发UI更新
        setTranslated(newTranslatedObject);

        // 4. 调用 saveArticle 并显式传递新对象，确保保存的是最新数据
        await saveArticle({ translated: newTranslatedObject });

        messageApi.success('译文合并编辑已保存');

        // 使用通用函数处理位置计算和滚动设置
        handlePositionCalculationAndScroll(
          'save',
          true, // isTranslated
          originalTranslatedEditingChunkIndex,
          setPendingTranslatedScrollTarget,
          () => {
            // 状态清理函数
            setIsMergedTranslatedEditing(false);
            setMergedTranslatedText('');
            setOriginalTranslatedEditingChunkIndex(null);
          }
        );
      } else {
        messageApi.warning('解析译文内容失败，请检查格式。提示：请确保内容为有效的HTML格式，或输入纯文本让系统自动转换。');
      }
    } catch (error) {
      console.error('保存译文合并编辑失败:', error);
      messageApi.error(`保存失败: ${error.message || '未知错误'}`);
    }
  }, [mergedTranslatedText, chunked, setTranslated, saveArticle, messageApi, originalTranslatedEditingChunkIndex, handlePositionCalculationAndScroll]);



  // handleTitleEdit has been moved above handleSaveTitleEdit to resolve initialization order issue.

  // Determine true loading state considering store sync
  const currentUuidFromParams = params.uuid; // Already defined as 'uuid' earlier, but re-aliasing for clarity here
  const isTrulyLoading = dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams));

  // 判断是否有文章内容 - 当没有文章数据且不在加载状态时，隐藏内容区域
  // 修复：确保当没有UUID参数时，不显示文章内容区域
  const hasArticleContent = !isTrulyLoading && currentUuidFromParams && articleFromStore && articleFromStore.uuid === currentUuidFromParams && (chunked && chunked.length > 0);

  // 调试：输出所有相关状态
  console.log('[ArticleEditPage] 调试信息:', {
    currentUuidFromParams,
    dataFetchingLoading,
    articleFromStore: articleFromStore ? { uuid: articleFromStore.uuid } : null,
    isTrulyLoading,
    hasArticleContent,
    chunkedLength: chunked ? chunked.length : 0,
    计算过程: {
      'dataFetchingLoading': dataFetchingLoading,
      'currentUuidFromParams存在': !!currentUuidFromParams,
      'articleFromStore不存在': !articleFromStore,
      'uuid匹配': articleFromStore && articleFromStore.uuid === currentUuidFromParams,
      'uuid不匹配': articleFromStore && articleFromStore.uuid !== currentUuidFromParams,
      '右侧条件': currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams),
      'hasArticleContent条件': !isTrulyLoading && currentUuidFromParams && articleFromStore && articleFromStore.uuid === currentUuidFromParams && (chunked && chunked.length > 0)
    }
  });





  if (isTrulyLoading && !dataFetchingLoading && currentUuidFromParams && articleFromStore?.uuid !== currentUuidFromParams) {
  }


  return (
    <div ref={pageContainerRef} className="article-edit-container" style={{padding: 'var(--toolbar-height, 80px) 24px 24px', paddingTop: 'var(--toolbar-height, 80px)'}}>
      {isTrulyLoading ? (
        <div style={{ textAlign: 'center', margin: '50px' }}>加载中...</div>
      ) : (
        <div>


          {/* ArticleControls JSX merged in */}
          <Row
            ref={toolbarRef}
            className={`article-controls ${isHeaderSticky ? 'toolbar-no-border' : ''}`}
            justify="space-between"
            align="middle" // Added for vertical alignment
            style={{
              position: 'fixed',
              top: 0,
              left: '250px', // Adjusted to match Sidebar width
              right: 0,
              zIndex: 1000,
              backgroundColor: '#f5f5f5', // Match Sidebar background color
              padding: '16px 24px 6px 24px', // Reduced vertical padding
              borderBottom: isHeaderSticky ? 'none' : '1px solid #e0e0e0' // 条件性地设置边框
            }}
          >
            <Col>
              <Space className="controls-section">
                <InputNumber
                  min={1}
                  value={displayedChunkSize}
                  onChange={handleDisplayChunkSizeChange}
                  onBlur={handleChunkSizeChangeImmediate}
                  onPressEnter={handleChunkSizeChangeImmediate}
                  style={{ width: '120px' }}
                  addonBefore="分块数"
                  controls={true}
                  size="small"
                  className="custom-small-font"
                />
                {isTranslatingAllActive ? (
                  <Button
                    type="primary"
                    danger
                    onClick={handleCancelTranslateAllHook}
                    disabled={isTranslationGloballyCancelled}
                    loading={isTranslationGloballyCancelled}
                    size="small"
                    className="custom-small-font"
                  >
                    停止翻译
                  </Button>
                ) : (
                  <Button
                    type="primary"
                    onClick={handleTranslateAllHook}
                    disabled={isTranslatingAllActive || isTranslationGloballyCancelled}
                    size="small"
                    className="custom-small-font"
                  >
                    翻译全文
                  </Button>
                )}
                <Button
                  onClick={() => startTerminologySyncProcess(chunked)}
                  disabled={!hasTranslations || isTranslatingAllActive} // 仅在有翻译内容且非全文翻译中时可用
                  size="small"
                  className="custom-small-font"
                >
                  术语统一
                </Button>

              </Space>
            </Col>

            <Col>
              <Space className="controls-section">
                {uuid && (
                  <Button type="default" onClick={handlePreview} size="small" className="custom-small-font">
                    预览译文
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
          {/* ArticleTitle JSX merged in - 当有UUID时显示，避免标题区域跳动 */}
          {currentUuidFromParams && (
            <Row style={{ margin: '10px 0', alignItems: 'center' }} gutter={8}>
              <Col flex="auto">
                {isTitleEditing ? (
                  <Space align="baseline" size={4}>
                    <Input
                      value={editingTitleText}
                      onChange={(e) => setEditingTitleText(e.target.value)}
                      onPressEnter={handleSaveTitleEdit}
                      onBlur={handleSaveTitleEdit} // Save on blur
                      style={{ fontSize: '1.2em', flexGrow: 1, minWidth: '300px' }}
                      placeholder="请输入标题"
                      autoFocus
                    />
                    <Button
                      type="text"
                      className="icon-title-button custom-small-font"
                      icon={<CloseOutlined style={{ fontSize: '14px' }} />}
                      onClick={cancelTitleEdit}
                      size="small"
                      style={{ color: '#595959' }}
                      title="取消编辑"
                    />
                  </Space>
                ) : (
                  <Space align="baseline" size={0}>
                    <Typography.Paragraph
                      style={{
                        fontSize: '1.2em',
                        marginBottom: '0',
                        marginRight: '8px',
                        flexGrow: 1,
                        minHeight: '1.5em', // 确保占位符有固定高度，避免跳动
                      }}
                    >
                      {isTitleLoading ? (
                        <span style={{ color: '#d9d9d9', fontStyle: 'italic' }}>正在加载标题...</span>
                      ) : (
                        articleTitle || "点击右侧按钮编辑标题"
                      )}
                    </Typography.Paragraph>
                    <Button
                      type="text"
                      className="icon-title-button custom-small-font"
                      icon={<EditOutlined style={{ fontSize: '14px' }} />}
                      onClick={startTitleEdit}
                      size="small"
                      title="编辑标题"
                      disabled={isTitleLoading} // 加载时禁用编辑按钮
                    />
                    <Button
                      type="text"
                      className="icon-title-button custom-small-font"
                      icon={<ThunderboltOutlined style={{ fontSize: '14px' }} />}
                      onClick={handleGenerateTitle}
                      loading={isGeneratingTitle}
                      size="small"
                      title="AI生成标题"
                      disabled={isTitleLoading || isGeneratingTitle} // 加载时禁用AI生成按钮
                    />
                  </Space>
                )}
              </Col>
            </Row>
          )}

          {/* AI总结区域 - 只在有文章内容时显示 */}
          {hasArticleContent && (
            <Row style={{ margin: '10px 0', alignItems: 'flex-start' }} gutter={8}>
              <Col flex="auto">
                <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                  {/* AI总结标题行 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px', marginBottom: '8px' }}>
                    <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>AI总结</h1>
                    {isGeneratingSummary ? (
                      <Button
                        type="text"
                        danger
                        size="small"
                        icon={<PauseOutlined />}
                        onClick={handleStopSummary}
                        loading={isStoppingSummary}
                        disabled={isStoppingSummary}
                        title="停止总结"
                      />
                    ) : (
                      <>
                        <Button
                          type="text"
                          className="icon-title-button custom-small-font"
                          icon={<ThunderboltOutlined style={{ fontSize: '14px' }} />}
                          onClick={handleGenerateSummary}
                          size="small"
                          title="AI生成总结"
                        />
                        {/* 移动过来的按钮 */}
                        <Popconfirm
                          title="确认清空总结?"
                          description="此操作将清空当前的AI总结内容。"
                          onConfirm={handleClearSummary}
                          okText="确认清空"
                          cancelText="取消"
                          okButtonProps={{ danger: true }}
                          disabled={!articleSummary}
                        >
                          <Button
                            type="text"
                            danger
                            icon={<ClearOutlined />}
                            size="small"
                            title="清空总结"
                            disabled={!articleSummary}
                          />
                        </Popconfirm>
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          size="small"
                          onClick={startSummaryEdit}
                          title="编辑总结"
                        />
                      </>
                    )}
                  </div>

                  {/* Think过程显示 */}
                  {(summaryThinkContent || isSummaryThinking) && (
                    <ThinkDisplay
                      thinkContent={summaryThinkContent}
                      isThinking={isSummaryThinking}
                      isCompleted={summaryThinkCompleted}
                      style={{ marginBottom: '12px' }}
                    />
                  )}

                  {/* 总结内容容器 */}
                  <div
                    className={`summary-content-container ${isSummaryEditing ? 'editing-mode' : ''}`}
                    style={{ flexGrow: 1, minHeight: isSummaryEditing ? '120px' : (isGeneratingSummary || articleSummary ? '80px' : '0px'), display: 'flex', flexDirection: 'column' }}
                  >
                    {isSummaryEditing ? (
                      <EnhanceTextArea
                        value={editingSummaryText}
                        onChange={(e) => setEditingSummaryText(e.target.value)}
                        showLineNumbers={true}
                        style={{ fontSize: '13px', width: '100%', resize: 'vertical' }}
                        placeholder="请输入总结内容"
                        autoSize={{ minRows: 4, maxRows: 20 }}
                        autoFocus
                      />
                    ) : (
                      <div style={{ minHeight: isGeneratingSummary || articleSummary ? '80px' : '0px' }}>
                        {isGeneratingSummary ? (
                          <Typography.Paragraph
                            style={{
                              fontSize: '13px',
                              marginBottom: '0',
                              lineHeight: '1.6',
                              whiteSpace: 'pre-wrap',
                              minHeight: '80px'
                            }}
                          >
                            {streamingSummaryText || '...'}
                          </Typography.Paragraph>
                        ) : articleSummary ? (
                          <Typography.Paragraph
                            style={{
                              fontSize: '13px',
                              marginBottom: '0',
                              lineHeight: '1.6',
                              whiteSpace: 'pre-wrap'
                            }}
                          >
                            {articleSummary}
                          </Typography.Paragraph>
                        ) : null}
                      </div>
                    )}
                  </div>

                  {/* 编辑模式下的按钮区域 */}
                  {isSummaryEditing && (
                    <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
                      <Space style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
                        <Button icon={<SaveOutlined />} size="small" onClick={handleSaveSummaryEdit}>保存</Button>
                        <Button icon={<CloseOutlined />} size="small" onClick={cancelSummaryEdit}>取消</Button>
                      </Space>
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          )}

          {/* 分隔线和内容区域 - 只在有文章内容时显示 */}
          {hasArticleContent && (
            <>
              <Divider style={{ margin: '16px 0' }} />

              {/* 内容区域标题 */}
              {!showTranslationOnly && (
                <div
                  className={`sticky-header-wrapper ${isStickyDisabled ? 'is-disabled' : (isHeaderSticky ? 'is-actually-sticky' : 'is-not-sticky')}`}
                  ref={stickyHeaderRef}
                >
                  <Row
                    className="content-titles-row"
                    style={{
                      alignItems: 'center',
                    }}
                    gutter={16}
                  >
                    {/* 原文标题 */}
                    <Col span={12}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                        <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>原文</h1>
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={handleStartMergedOriginalEdit}
                          disabled={isMergedOriginalEditing}
                          size="small"
                          title="编辑原文全文"
                        />
                        <Popconfirm
                          title="确认清空原文?"
                          description="此操作将清空所有原文内容，无法撤销。"
                          onConfirm={() => {
                            setContent([]);
                            messageApi.success('原文已清空');
                          }}
                          okText="确认清空"
                          cancelText="取消"
                          okButtonProps={{ danger: true }}
                          disabled={!chunked || chunked.length === 0 || isMergedOriginalEditing}
                        >
                          <Button
                            type="text"
                            danger
                            icon={<ClearOutlined />}
                            disabled={!chunked || chunked.length === 0 || isMergedOriginalEditing}
                            size="small"
                            title="清空原文全文"
                          />
                        </Popconfirm>
                      </div>
                    </Col>

                    {/* 译文标题 */}
                    <Col span={12}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                        <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>译文</h1>
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={handleStartMergedTranslatedEdit}
                          disabled={isMergedTranslatedEditing}
                          size="small"
                          title="编辑译文全文"
                        />
                        <Popconfirm
                          title="确认清空译文?"
                          description="此操作将清空所有译文内容，但原文保留。"
                          onConfirm={handleClearAllHook}
                          okText="确认清空"
                          cancelText="取消"
                          okButtonProps={{ danger: true }}
                          disabled={!hasTranslations || isMergedTranslatedEditing}
                        >
                          <Button
                            type="text"
                            danger
                            icon={<ClearOutlined />}
                            disabled={!hasTranslations || isMergedTranslatedEditing}
                            size="small"
                            title="清空译文全文"
                          />
                        </Popconfirm>
                      </div>
                    </Col>
                  </Row>
                </div>
              )}

              {/* 合并编辑模式或正常chunk显示模式 */}
              <Space direction="vertical" style={{ width: '100%' }} className="t-content">
                {chunked.map((items, chunkIdx) => {
                  const translatedItemsForChunk = allChunkTranslatedItems[chunkIdx] || [];
                  return (
                    <ChunkRenderer
                      key={`chunk-renderer-${chunkIdx}`}
                      rowId={`chunk-row-${chunkIdx}`} // 新增：传递 id
                      items={items}
                      translatedItemsForChunk={translatedItemsForChunk}
                      chunkIndex={chunkIdx}
                      editingState={editingState}
                      editText={editingState.index === chunkIdx ? editText : ''}
                      debouncedSetEditText={debouncedSetEditTextHook}
                      handleEditStart={handleEditStartHook}
                      handleEditSave={handleEditSaveHook}
                      handleEditCancel={handleEditCancelHook}
                      isActivelyTranslating={isChunkTranslating(chunkIdx)}
                      disableTranslateButton={shouldDisableTranslateButton(chunkIdx)}
                      stoppingChunks={stoppingChunks}
                      handleTranslateChunk={(idx) => handleTranslateChunkHook(idx, items, shouldDisableTranslateButton)}
                      handleStopChunkTranslation={handleStopChunkTranslationHook}
                      handleClearTranslatedChunk={(idx) => handleClearTranslatedChunkHook(idx, items)}
                      handleDeleteChunk={handleDeleteChunk}
                      showTranslationOnly={showTranslationOnly}
                      renderTranslatedContentInternal={renderTranslatedContent}
                      translationContainerRefs={translationContainerRefs}
                      updateTranslationDOM={stableUpdateTranslationDOM} // Pass stable version
                      checkTagCountMismatch={checkTagCountMismatch}
                      // 传递合并编辑状态，让ChunkRenderer知道是否需要隐藏某些部分
                      isMergedOriginalEditing={isMergedOriginalEditing}
                      isMergedTranslatedEditing={isMergedTranslatedEditing}
                      // 传递合并编辑相关的数据和处理函数
                      mergedOriginalText={mergedOriginalText}
                      setMergedOriginalText={setMergedOriginalText}
                      handleSaveMergedOriginalEdit={handleSaveMergedOriginalEdit}
                      handleCancelMergedOriginalEdit={handleCancelMergedOriginalEdit}
                      mergedTranslatedText={mergedTranslatedText}
                      setMergedTranslatedText={setMergedTranslatedText}
                      handleSaveMergedTranslatedEdit={handleSaveMergedTranslatedEdit}
                      handleCancelMergedTranslatedEdit={handleCancelMergedTranslatedEdit}
                      // 传递动态高度信息
                      fullPageEditorRef={fullPageEditorRef} // 新增：传递 ref
                    />
                  );
                })}
              </Space>
            </>
          )}

          {/* 来源区域 - 只在有文章内容时显示 */}
          {hasArticleContent && (
            <Row style={{ margin: '10px 0', alignItems: 'flex-start' }} gutter={8}>
              <Col flex="auto">
                <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                  {/* 来源标题行 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                    <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>来源</h1>
                  </div>

                  {/* 来源内容容器 */}
                  <div
                    className={`citation-content-container ${isCitationEditing ? 'editing-mode' : ''}`}
                    style={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}
                  >
                    {isCitationEditing ? (
                      <EnhanceTextArea
                        value={editingCitationText}
                        onChange={(e) => setEditingCitationText(e.target.value)}
                        showLineNumbers={true}
                        style={{ fontSize: '13px', width: '100%', resize: 'vertical' }}
                        placeholder="请输入来源内容..."
                        autoSize={{ minRows: 3, maxRows: 20 }}
                        autoFocus
                      />
                    ) : (
                      <div>
                        {citationData ? (
                          <Typography.Paragraph
                            style={{
                              fontSize: '13px',
                              marginBottom: '0',
                              lineHeight: '1.6',
                              whiteSpace: 'pre-wrap'
                            }}
                          >
                            {citationData}
                          </Typography.Paragraph>
                        ) : null}
                      </div>
                    )}
                  </div>

                  {/* 按钮区域 - 模仿AI总结的按钮布局 */}
                  <div style={{ marginTop: 'auto', display: 'flex', justifyContent: citationData ? 'flex-end' : 'flex-start' }}>
                    {isCitationEditing ? (
                      <Space style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
                        <Button icon={<SaveOutlined />} size="small" onClick={handleSaveCitation}>保存</Button>
                        <Button icon={<CloseOutlined />} size="small" onClick={cancelCitationEdit}>取消</Button>
                      </Space>
                    ) : (
                      <Space align="baseline" size={0}>
                        <Popconfirm
                          title="确认清空来源?"
                          description="此操作将清空当前的来源内容。"
                          onConfirm={handleClearCitation}
                          okText="确认清空"
                          cancelText="取消"
                          okButtonProps={{ danger: true }}
                          disabled={!citationData}
                        >
                          <Button
                            type="text"
                            danger
                            icon={<ClearOutlined />}
                            size="small"
                            title="清空来源"
                            disabled={!citationData}
                          />
                        </Popconfirm>
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          size="small"
                          onClick={startCitationEdit}
                          title="编辑来源"
                        />
                      </Space>
                    )}
                  </div>
                </div>
              </Col>
            </Row>
          )}

          {/* 参考区域 - 只在有文章内容时显示 */}
          {hasArticleContent && (
            <Row style={{ margin: '10px 0', alignItems: 'flex-start' }} gutter={8}>
              <Col flex="auto">
                <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                  {/* 参考标题行 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                    <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>参考</h1>
                  </div>

                  {/* 参考内容容器 */}
                  <div
                    className={`refs-content-container ${isRefsEditing ? 'editing-mode' : ''}`}
                    style={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}
                  >
                    {isRefsEditing ? (
                      <EnhanceTextArea
                        value={editingRefsText}
                        onChange={(e) => setEditingRefsText(e.target.value)}
                        showLineNumbers={true}
                        style={{ fontSize: '13px', width: '100%', resize: 'vertical' }}
                        placeholder="请输入参考内容..."
                        autoSize={{ minRows: 3, maxRows: 20 }}
                        autoFocus
                      />
                    ) : (
                      <div>
                        {refsData ? (
                          <Typography.Paragraph
                            style={{
                              fontSize: '13px',
                              marginBottom: '0',
                              lineHeight: '1.6',
                              whiteSpace: 'pre-wrap'
                            }}
                          >
                            {refsData}
                          </Typography.Paragraph>
                        ) : null}
                      </div>
                    )}
                  </div>

                  {/* 按钮区域 - 模仿AI总结的按钮布局 */}
                  <div style={{ marginTop: 'auto', display: 'flex', justifyContent: refsData ? 'flex-end' : 'flex-start' }}>
                    {isRefsEditing ? (
                      <Space style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
                        <Button
                          icon={<RobotOutlined />}
                          size="small"
                          onClick={handleParseRefs}
                          loading={isParsingRefs}
                          disabled={!editingRefsText.trim()}
                        >
                          AI格式化
                        </Button>
                        <Button icon={<SaveOutlined />} size="small" onClick={handleSaveRefs}>保存</Button>
                        <Button icon={<CloseOutlined />} size="small" onClick={cancelRefsEdit}>取消</Button>
                      </Space>
                    ) : (
                      <Space align="baseline" size={0}>
                        <Popconfirm
                          title="确认清空参考?"
                          description="此操作将清空当前的参考内容。"
                          onConfirm={handleClearRefs}
                          okText="确认清空"
                          cancelText="取消"
                          okButtonProps={{ danger: true }}
                          disabled={!refsData}
                        >
                          <Button
                            type="text"
                            danger
                            icon={<ClearOutlined />}
                            size="small"
                            title="清空参考"
                            disabled={!refsData}
                          />
                        </Popconfirm>
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          size="small"
                          onClick={startRefsEdit}
                          title="编辑参考"
                        />
                      </Space>
                    )}
                  </div>
                </div>
              </Col>
            </Row>
          )}
        </div>
      )}
      {/* 术语同步模态框 */}
      <TerminologySyncModal
        open={isTerminologyModalOpen}
        terms={terminologyListForModal}
        progress={terminologyExtractionProgress}
        onConfirm={applyTerminologySync} // 直接使用 store 中的 action
        onCancel={closeTerminologyModal} // 直接使用 store 中的 action
      />


    </div>
  );
};

export default memo(ArticleEditPage);
